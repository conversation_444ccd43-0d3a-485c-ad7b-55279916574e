-- 修复幸运转盘奖品排序问题
-- 问题：转盘指针指向与中奖结果不匹配
-- 原因：数据库中奖品的 sort_order 字段顺序混乱，导致前端转盘绘制顺序与后端索引计算不一致

USE lianliankan;

-- 开始事务
START TRANSACTION;

-- 修正奖品排序顺序，确保按等级从高到低排列
-- 这样前端转盘绘制的顺序与后端返回的索引就能完全匹配

-- 一等奖：sort_order = 0 (12点钟位置)
UPDATE lucky_wheel_prizes SET sort_order = 0 WHERE id = 1 AND name = '一等奖';

-- 二等奖：sort_order = 1 
UPDATE lucky_wheel_prizes SET sort_order = 1 WHERE id = 2 AND name = '二等奖';

-- 三等奖：sort_order = 2
UPDATE lucky_wheel_prizes SET sort_order = 2 WHERE id = 3 AND name = '三等奖';

-- 四等奖：sort_order = 3
UPDATE lucky_wheel_prizes SET sort_order = 3 WHERE id = 4 AND name = '四等奖';

-- 五等奖：sort_order = 4
UPDATE lucky_wheel_prizes SET sort_order = 4 WHERE id = 5 AND name = '五等奖';

-- 参与奖：sort_order = 5
UPDATE lucky_wheel_prizes SET sort_order = 5 WHERE id = 6 AND name = '参与奖';

-- 验证修复结果
SELECT 
    id,
    name,
    sort_order,
    probability,
    remaining_quantity,
    is_active
FROM lucky_wheel_prizes 
ORDER BY sort_order ASC;

-- 提交事务
COMMIT;

-- 说明：
-- 修复后的奖品顺序（从12点钟开始顺时针）：
-- 0. 一等奖 (12点钟)
-- 1. 二等奖 (2点钟)  
-- 2. 三等奖 (4点钟)
-- 3. 四等奖 (6点钟)
-- 4. 五等奖 (8点钟)
-- 5. 参与奖 (10点钟)
--
-- 这样当后端返回 prizeIndex = 0 时，转盘会正确转到一等奖位置
-- 当后端返回 prizeIndex = 1 时，转盘会正确转到二等奖位置，以此类推

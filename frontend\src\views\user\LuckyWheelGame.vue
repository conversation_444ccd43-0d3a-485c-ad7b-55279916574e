<template>
  <div class="lucky-wheel-game">
    <!-- 通用导航栏 -->
    <CommonNavbar />

    <!-- CDK输入阶段 -->
    <div v-if="gameState === 'cdk-input'" class="cdk-input-stage">
      <div class="stage-header">
        <p>请输入您的CDK代码开始游戏</p>
      </div>
      
      <div class="cdk-input-form">
        <div class="input-group">
          <input
            v-model="cdkInput"
            type="text"
            placeholder="请输入CDK代码"
            :disabled="isValidating"
            @keyup.enter="validateCDK"
            class="cdk-input"
          />
          <button 
            @click="validateCDK" 
            :disabled="!cdkInput.trim() || isValidating"
            class="validate-btn"
          >
            {{ isValidating ? '验证中...' : '开始游戏' }}
          </button>
        </div>
        
        <div v-if="cdkError" class="error-message">
          {{ cdkError }}
        </div>
      </div>
      
      <div class="game-info">
        <h3>游戏说明</h3>
        <ul>
          <li>输入有效的CDK代码即可开始游戏</li>
          <li>每个CDK有固定的转盘次数</li>
          <li>转盘结果完全随机，祝您好运！</li>
        </ul>
      </div>
    </div>

    <!-- 游戏进行阶段 -->
    <div v-if="gameState === 'playing'" class="game-stage">
      <div class="stage-header">
        <div class="game-status">
          <span class="cdk-info">CDK: {{ currentCDK }}</span>
          <span class="spins-info">剩余次数: {{ remainingSpins }}</span>
        </div>
      </div>

      <!-- 转盘区域 -->
      <div class="wheel-container">
        <div class="wheel-wrapper">
          <canvas 
            ref="wheelCanvas" 
            :width="wheelSize" 
            :height="wheelSize"
            class="wheel-canvas"
            :class="{ spinning: isSpinning }"
          ></canvas>
          
          <div class="wheel-pointer"></div>
          
          <button
            @click="spin"
            :disabled="!canSpin || isSpinning"
            class="spin-button"
            :class="{ hidden: isSpinning }"
          >
            {{ getSpinButtonText() }}
          </button>
        </div>
      </div>

      <!-- 游戏控制 -->
      <div class="game-controls">
        <button @click="showHistory = !showHistory" class="history-btn">
          {{ showHistory ? '隐藏' : '查看' }}历史记录
        </button>
        <button @click="resetGame" class="reset-btn">重新开始</button>
      </div>

      <!-- 历史记录 -->
      <div v-if="showHistory" class="spin-history">
        <h3>转盘历史</h3>
        <div v-if="spinHistory.length === 0" class="no-history">
          暂无转盘记录
        </div>
        <div v-else class="history-list">
          <div 
            v-for="(record, index) in spinHistory" 
            :key="index"
            class="history-item"
            :class="{ winning: record.isWinning }"
          >
            <div class="history-time">{{ formatTime(record.spinTime) }}</div>
            <div class="history-result">
              {{ record.isWinning ? `🎉 ${record.prize.name}` : '😔 未中奖' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果弹窗 -->
    <div v-if="showResultDialog" class="result-overlay" @click="closeResultDialog">
      <div class="result-dialog" @click.stop>
        <div class="result-content">
          <div v-if="lastSpinResult.isWinning" class="winning-result">
            <div class="result-icon">🎉</div>
            <h2>恭喜中奖！</h2>
            <div class="prize-info">
              <div class="prize-name">{{ lastSpinResult.prize.name }}</div>
              <div v-if="lastSpinResult.prize.description" class="prize-desc">
                {{ lastSpinResult.prize.description }}
              </div>
            </div>
          </div>
          
          <div v-else class="losing-result">
            <div class="result-icon">😔</div>
            <h2>很遗憾，未中奖</h2>
            <p>不要灰心，继续尝试吧！</p>
          </div>
          
          <div class="remaining-info">
            剩余转盘次数: {{ remainingSpins }}
          </div>
        </div>
        
        <div class="result-actions">
          <button @click="closeResultDialog" class="continue-btn">
            {{ remainingSpins > 0 ? '继续游戏' : '游戏结束' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
  </div>
</template>

<script>
import { luckyWheelApi } from '../../api/luckyWheelApi'
import CommonNavbar from '../../components/CommonNavbar.vue'

export default {
  name: 'LuckyWheelGame',
  components: {
    CommonNavbar
  },
  data() {
    return {
      gameState: 'cdk-input', // 'cdk-input', 'playing'
      
      // CDK相关
      cdkInput: '',
      currentCDK: '',
      isValidating: false,
      cdkError: '',
      
      // 游戏状态
      sessionId: '',
      remainingSpins: 0,
      canSpin: false,
      isSpinning: false,
      spinHistory: [],
      showHistory: false,
      
      // 转盘相关
      wheelSize: 400,
      prizes: [],
      currentRotation: 0,
      
      // 结果相关
      showResultDialog: false,
      lastSpinResult: null,
      
      // 加载状态
      isLoading: false,
      loadingText: ''
    }
  },
  
  mounted() {
    // 检查URL参数中是否有CDK
    const urlParams = new URLSearchParams(window.location.search)
    const cdkFromUrl = urlParams.get('cdk')
    if (cdkFromUrl) {
      this.cdkInput = cdkFromUrl
      this.validateCDK()
    }
    
    // 调整转盘大小
    this.adjustWheelSize()
    window.addEventListener('resize', this.adjustWheelSize)
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.adjustWheelSize)
  },
  
  methods: {
    async validateCDK() {
      if (!this.cdkInput.trim()) return
      
      this.isValidating = true
      this.cdkError = ''
      
      try {
        const response = await luckyWheelApi.validateCDK(this.cdkInput.trim())
        
        if (response.code === 200 && response.data.isValid) {
          this.currentCDK = this.cdkInput.trim()
          this.sessionId = response.data.sessionId
          this.remainingSpins = response.data.remainingSpins
          this.canSpin = this.remainingSpins > 0
          
          // 加载奖品数据
          await this.loadPrizes()
          
          // 切换到游戏阶段
          this.gameState = 'playing'
          
          // 绘制转盘
          this.$nextTick(() => {
            this.drawWheel()
          })
          
        } else {
          this.cdkError = response.data.message || 'CDK验证失败'
        }
      } catch (error) {
        console.error('CDK验证失败:', error)
        this.cdkError = '验证失败，请检查网络连接'
      } finally {
        this.isValidating = false
      }
    },
    
    async loadPrizes() {
      try {
        const response = await luckyWheelApi.getAvailablePrizes()
        if (response.code === 200) {
          this.prizes = response.data

          // 🎯 数据一致性验证：检查奖品列表
          this.validatePrizeConsistency()
        }
      } catch (error) {
        console.error('加载奖品失败:', error)
      }
    },

    // 🎯 新增：验证奖品列表一致性
    validatePrizeConsistency() {
      if (!this.prizes || this.prizes.length === 0) {
        console.warn('🎯 奖品列表为空')
        return
      }

      // 检查奖品是否按sortOrder排序
      const sortedPrizes = [...this.prizes].sort((a, b) => a.sortOrder - b.sortOrder)
      const isCorrectlySorted = this.prizes.every((prize, index) =>
        prize.id === sortedPrizes[index].id
      )

      if (!isCorrectlySorted) {
        console.error('🎯 奖品列表排序不正确！', {
          当前顺序: this.prizes.map(p => `${p.sortOrder}:${p.name}`),
          正确顺序: sortedPrizes.map(p => `${p.sortOrder}:${p.name}`)
        })
      }

      // 检查是否有重复的sortOrder
      const sortOrders = this.prizes.map(p => p.sortOrder)
      const uniqueSortOrders = [...new Set(sortOrders)]
      if (sortOrders.length !== uniqueSortOrders.length) {
        console.error('🎯 发现重复的sortOrder！', sortOrders)
      }

      console.log('🎯 奖品列表一致性检查:', {
        奖品总数: this.prizes.length,
        排序正确: isCorrectlySorted,
        无重复排序: sortOrders.length === uniqueSortOrders.length,
        奖品详情: this.prizes.map((p, i) => ({
          索引: i,
          ID: p.id,
          名称: p.name,
          排序: p.sortOrder,
          库存: p.remainingQuantity
        }))
      })
    },
    
    async spin() {
      if (!this.canSpin || this.isSpinning) return

      // 🎯 转盘前验证：确保奖品列表是最新的
      await this.loadPrizes()

      this.isSpinning = true

      try {
        const response = await luckyWheelApi.spin(this.sessionId, this.currentCDK)

        if (response.code === 200 && response.data.success) {
          this.lastSpinResult = response.data
          this.remainingSpins = response.data.remainingSpins
          this.canSpin = this.remainingSpins > 0

          // 🎯 验证返回的索引是否有效
          if (response.data.isWinning && response.data.prizeIndex !== null) {
            if (response.data.prizeIndex < 0 || response.data.prizeIndex >= this.prizes.length) {
              console.error('🎯 后端返回的奖品索引无效！', {
                返回索引: response.data.prizeIndex,
                奖品总数: this.prizes.length,
                中奖奖品: response.data.prize
              })
              alert('转盘结果异常，请联系管理员')
              return
            }

            // 验证索引对应的奖品是否匹配
            const expectedPrize = this.prizes[response.data.prizeIndex]
            if (expectedPrize.id !== response.data.prize.id) {
              console.error('🎯 奖品索引与实际奖品不匹配！', {
                索引: response.data.prizeIndex,
                索引对应奖品: expectedPrize,
                实际中奖奖品: response.data.prize,
                '=== 当前奖品列表 ===': '',
                前端奖品列表: this.prizes.map((p, i) => `索引${i}: ${p.name} (ID:${p.id}, 库存:${p.remainingQuantity})`),
                '=== 后端返回数据 ===': '',
                后端返回索引: response.data.prizeIndex,
                后端返回奖品: `${response.data.prize.name} (ID:${response.data.prize.id})`
              })
              alert('转盘结果异常，请联系管理员')
              return
            }

            console.log('🎯 奖品索引验证通过！', {
              索引: response.data.prizeIndex,
              奖品名称: expectedPrize.name,
              奖品ID: expectedPrize.id,
              匹配状态: '✅ 正确'
            })
          }

          // 🎯 关键修复：动画前再次同步奖品列表，确保索引一致
          await this.loadPrizes()

          // 🎯 修复：执行转盘动画，使用后端返回的奖品索引
          await this.performSpinAnimation(response.data.prizeIndex, response.data.isWinning)

          // 更新历史记录
          await this.loadGameState()

          // 显示结果
          this.showResultDialog = true

        } else {
          alert(response.data.message || '转盘失败')
        }
      } catch (error) {
        console.error('转盘失败:', error)
        alert('转盘失败，请重试')
      } finally {
        this.isSpinning = false
      }
    },
    
    async loadGameState() {
      try {
        const response = await luckyWheelApi.getGameState(this.sessionId, this.currentCDK)
        if (response.code === 200) {
          this.spinHistory = response.data.spinHistory || []
          this.remainingSpins = response.data.remainingSpins
          this.canSpin = response.data.canSpin
        }
      } catch (error) {
        console.error('加载游戏状态失败:', error)
      }
    },
    
    performSpinAnimation(prizeIndex, isWinning) {
      return new Promise((resolve) => {
        const canvas = this.$refs.wheelCanvas
        if (!canvas) {
          resolve()
          return
        }

        // 🎯 修复：直接使用后端返回的奖品索引，无需查找
        if (!isWinning || prizeIndex === null || prizeIndex === undefined) {
          // 未中奖时，随机选择一个位置（但不会显示中奖结果）
          const randomIndex = Math.floor(Math.random() * this.prizes.length)
          prizeIndex = randomIndex
          console.log('🎯 未中奖，随机转到索引:', randomIndex)
        }

        // 验证索引有效性
        if (prizeIndex < 0 || prizeIndex >= this.prizes.length) {
          console.error('🎯 奖品索引无效:', prizeIndex, '奖品总数:', this.prizes.length)
          resolve()
          return
        }

        // 🎯 **修正版本**：与绘制逻辑完全一致
        const totalPrizes = this.prizes.length
        const anglePerPrize = 360 / totalPrizes

        // 基础旋转圈数
        const minSpins = 5
        const maxSpins = 8
        const randomSpins = minSpins + Math.random() * (maxSpins - minSpins)

        // 🎯 关键：计算目标奖品的中心角度
        // 绘制时：第一个奖品（索引0）在12点钟，即0°
        // 目标奖品的中心角度：prizeIndex * anglePerPrize
        const targetAngle = prizeIndex * anglePerPrize

        // 🎯 要让目标奖品转到12点钟位置，需要转动的角度
        // 因为转盘顺时针旋转，所以用负值让目标奖品转到12点钟
        const adjustmentAngle = -targetAngle

        // 最终旋转角度
        const finalRotation = randomSpins * 360 + adjustmentAngle

        // 🎯 累加到当前旋转角度
        this.currentRotation += finalRotation

        console.log('🎯 转盘动画 - 修正版本:', {
          '=== 基本信息 ===': '',
          是否中奖: isWinning,
          后端返回索引: prizeIndex,
          目标奖品: this.prizes[prizeIndex]?.name || '未知',
          奖品总数: totalPrizes,
          每个奖品角度: anglePerPrize.toFixed(1) + '°',

          '=== 角度计算（与绘制逻辑一致） ===': '',
          随机圈数: randomSpins.toFixed(2),
          目标角度: targetAngle.toFixed(1) + '°',
          调整角度: adjustmentAngle.toFixed(1) + '°',
          本次旋转: finalRotation.toFixed(1) + '°',
          累计旋转: this.currentRotation.toFixed(1) + '°',

          '=== 验证信息 ===': '',
          指针位置: '12点钟方向(0°)',
          预期结果: `索引${prizeIndex}的奖品应对准指针`
        })

        // 🎯 执行动画 - 使用累计旋转角度（照搬另一个转盘）
        canvas.style.transform = `rotate(${this.currentRotation}deg)`
        canvas.style.transition = 'transform 3s cubic-bezier(0.25, 0.46, 0.45, 0.94)'

        setTimeout(() => {
          canvas.style.transition = ''

          // 🎯 验证：使用另一个转盘的指针位置计算逻辑
          this.verifySpinResultLikeOtherWheel(prizeIndex)

          resolve()
        }, 3000)
      })
    },

    // 🎯 辅助方法：将角度转换为时钟位置描述
    getClockPosition(angle) {
      const positions = [
        { angle: 0, desc: '12点钟' },
        { angle: 30, desc: '1点钟' },
        { angle: 60, desc: '2点钟' },
        { angle: 90, desc: '3点钟' },
        { angle: 120, desc: '4点钟' },
        { angle: 150, desc: '5点钟' },
        { angle: 180, desc: '6点钟' },
        { angle: 210, desc: '7点钟' },
        { angle: 240, desc: '8点钟' },
        { angle: 270, desc: '9点钟' },
        { angle: 300, desc: '10点钟' },
        { angle: 330, desc: '11点钟' }
      ]

      const closest = positions.reduce((prev, curr) => {
        return Math.abs(curr.angle - angle) < Math.abs(prev.angle - angle) ? curr : prev
      })

      return closest.desc
    },

    // 🎯 验证转盘结果：与绘制逻辑完全一致
    verifySpinResultLikeOtherWheel(expectedPrizeIndex) {
      const totalPrizes = this.prizes.length
      const anglePerPrize = 360 / totalPrizes

      // 🎯 关键：与绘制逻辑保持一致
      // 绘制时：offsetAngle = -90°，第一个奖品在12点钟
      // 指针在12点钟，所以需要计算哪个奖品的中心角度对准12点钟

      // 当前旋转后，12点钟指针实际指向的角度
      const currentPointerAngle = (this.currentRotation % 360 + 360) % 360

      // 🎯 重要：考虑绘制时的-90°偏移
      // 第一个奖品（索引0）的中心在12点钟，即0°位置
      // 每个奖品的中心角度：index * anglePerPrize

      // 计算指针指向哪个奖品
      // 因为转盘顺时针旋转，指针相对逆时针移动
      const relativeAngle = (360 - currentPointerAngle) % 360
      const actualPrizeIndex = Math.floor(relativeAngle / anglePerPrize) % totalPrizes

      console.log('🎯 转盘结果验证（与绘制逻辑一致）:', {
        累计旋转角度: this.currentRotation.toFixed(1) + '°',
        当前指针角度: currentPointerAngle.toFixed(1) + '°',
        相对角度: relativeAngle.toFixed(1) + '°',
        每个奖品角度: anglePerPrize.toFixed(1) + '°',
        期望奖品索引: expectedPrizeIndex,
        实际指向索引: actualPrizeIndex,
        期望奖品: this.prizes[expectedPrizeIndex]?.name || '未知',
        实际指向奖品: this.prizes[actualPrizeIndex]?.name || '未知',
        结果匹配: expectedPrizeIndex === actualPrizeIndex ? '✅ 正确' : '❌ 错误'
      })

      return actualPrizeIndex === expectedPrizeIndex
    },

    drawWheel() {
      const canvas = this.$refs.wheelCanvas
      if (!canvas || this.prizes.length === 0) return

      const ctx = canvas.getContext('2d')
      const centerX = this.wheelSize / 2
      const centerY = this.wheelSize / 2
      const radius = this.wheelSize / 2 - 10

      // 清空画布
      ctx.clearRect(0, 0, this.wheelSize, this.wheelSize)

      // 绘制转盘扇形
      const anglePerPrize = (2 * Math.PI) / this.prizes.length
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']

      // 调整起始角度，让第一个奖品在12点钟方向
      const offsetAngle = -Math.PI / 2 // 向上偏移90度，让第一个奖品在顶部

      // 🎯 详细调试：打印转盘布局
      console.log('🎯 转盘绘制布局详情:', this.prizes.map((prize, index) => {
        const startAngleDeg = (index * anglePerPrize + offsetAngle) * 180 / Math.PI
        const endAngleDeg = ((index + 1) * anglePerPrize + offsetAngle) * 180 / Math.PI
        const centerAngleDeg = startAngleDeg + (endAngleDeg - startAngleDeg) / 2

        // 标准化角度到0-360范围
        const normalizedCenter = ((centerAngleDeg % 360) + 360) % 360

        return {
          index,
          name: prize.name,
          startAngle: startAngleDeg.toFixed(1) + '°',
          centerAngle: centerAngleDeg.toFixed(1) + '°',
          normalizedCenter: normalizedCenter.toFixed(1) + '°',
          endAngle: endAngleDeg.toFixed(1) + '°',
          位置描述: this.getClockPosition(normalizedCenter)
        }
      }))

      // 🎯 额外调试：显示12点钟方向应该是哪个奖品
      console.log('🎯 12点钟方向（0°）对应的奖品:', this.prizes[0]?.name)

      this.prizes.forEach((prize, index) => {
        const startAngle = index * anglePerPrize + offsetAngle
        const endAngle = (index + 1) * anglePerPrize + offsetAngle

        // 绘制扇形
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, startAngle, endAngle)
        ctx.closePath()
        ctx.fillStyle = colors[index % colors.length]
        ctx.fill()
        ctx.strokeStyle = '#fff'
        ctx.lineWidth = 2
        ctx.stroke()

        // 绘制文字
        ctx.save()
        ctx.translate(centerX, centerY)
        ctx.rotate(startAngle + anglePerPrize / 2)
        ctx.textAlign = 'center'
        ctx.fillStyle = '#333'
        ctx.font = 'bold 14px Arial'
        ctx.fillText(prize.name, radius * 0.7, 5)
        ctx.restore()
      })

      // 绘制中心圆
      ctx.beginPath()
      ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI)
      ctx.fillStyle = '#333'
      ctx.fill()
    },

    adjustWheelSize() {
      const container = document.querySelector('.wheel-container')
      if (container) {
        const containerWidth = container.clientWidth
        this.wheelSize = Math.min(400, containerWidth - 40)
      }
    },

    resetGame() {
      this.gameState = 'cdk-input'
      this.cdkInput = ''
      this.currentCDK = ''
      this.sessionId = ''
      this.remainingSpins = 0
      this.canSpin = false
      this.spinHistory = []
      this.showHistory = false
      this.cdkError = ''
      this.showResultDialog = false
      this.lastSpinResult = null
    },

    closeResultDialog() {
      this.showResultDialog = false
      if (this.remainingSpins <= 0) {
        // 游戏结束，返回CDK输入阶段
        this.resetGame()
      }
    },

    getSpinButtonText() {
      if (this.isSpinning) return '转盘中...'
      if (!this.canSpin) return '无法转盘'
      return '开始转盘'
    },

    formatTime(timeString) {
      if (!timeString) return ''
      return new Date(timeString).toLocaleTimeString('zh-CN')
    },

    setLoading(loading, text = '') {
      this.isLoading = loading
      this.loadingText = text
    }
  }
}
</script>

<style scoped>
.lucky-wheel-game {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.cdk-input-stage,
.game-stage {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;
}

/* CDK输入阶段 */
.cdk-input-stage {
  text-align: center;
}

.cdk-input-stage > * {
  max-width: 500px;
  width: 100%;
}

.stage-header {
  margin-bottom: 30px;
  text-align: center;
}

.stage-header p {
  color: rgba(255,255,255,0.9);
  font-size: 18px;
  margin: 0;
}

.cdk-input-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  margin-bottom: 30px;
}

.input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.cdk-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  text-transform: uppercase;
}

.validate-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.validate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
}

.game-info {
  background: rgba(255,255,255,0.1);
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.game-info h3 {
  color: white;
  margin-bottom: 15px;
}

.game-info ul {
  color: rgba(255,255,255,0.9);
  text-align: left;
  list-style: none;
  padding: 0;
}

.game-info li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.game-info li:before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4ECDC4;
}

/* 游戏阶段 */
.game-stage {
  text-align: center;
}

.game-stage > * {
  max-width: 600px;
  width: 100%;
}

.game-status {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.cdk-info, .spins-info {
  background: rgba(255,255,255,0.2);
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

/* 转盘区域 */
.wheel-container {
  margin: 30px 0;
  display: flex;
  justify-content: center;
}

.wheel-wrapper {
  position: relative;
  display: inline-block;
}

.wheel-canvas {
  border-radius: 50%;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}

.wheel-pointer {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid #ff4757;
  z-index: 10;
}

.spin-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
  transition: all 0.3s;
  z-index: 10;
}

.spin-button:hover:not(:disabled) {
  transform: translate(-50%, -50%) scale(1.1);
}

.spin-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: translate(-50%, -50%);
}

.spin-button.hidden {
  opacity: 0;
  pointer-events: none;
}

/* 游戏控制 */
.game-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.history-btn, .reset-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.history-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.reset-btn {
  background: #ff4757;
  color: white;
}

.history-btn:hover, .reset-btn:hover {
  transform: translateY(-2px);
}

/* 历史记录 */
.spin-history {
  background: rgba(255,255,255,0.1);
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  margin-top: 20px;
}

.spin-history h3 {
  color: white;
  margin-bottom: 15px;
}

.no-history {
  color: rgba(255,255,255,0.7);
  font-style: italic;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  background: rgba(255,255,255,0.1);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-item.winning {
  background: rgba(76, 175, 80, 0.3);
}

.history-time {
  color: rgba(255,255,255,0.8);
  font-size: 12px;
}

.history-result {
  color: white;
  font-weight: 500;
}

/* 结果弹窗 */
.result-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-dialog {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.result-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.winning-result h2 {
  color: #4CAF50;
  margin-bottom: 20px;
}

.losing-result h2 {
  color: #f44336;
  margin-bottom: 15px;
}

.prize-info {
  margin-bottom: 20px;
}

.prize-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.prize-desc {
  color: #666;
  font-size: 14px;
}

.remaining-info {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 6px;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.continue-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  margin-top: 20px;
  font-size: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cdk-input-stage,
  .game-stage {
    padding: 10px;
  }

  .stage-header p {
    font-size: 16px;
  }

  .cdk-input-form {
    padding: 20px;
  }

  .input-group {
    flex-direction: column;
  }

  .game-status {
    flex-direction: column;
    gap: 10px;
  }

  .wheel-canvas {
    width: 300px !important;
    height: 300px !important;
  }

  .spin-button {
    width: 60px;
    height: 60px;
    font-size: 12px;
  }

  .game-controls {
    flex-direction: column;
    gap: 10px;
  }

  .result-dialog {
    padding: 20px;
  }

  .result-icon {
    font-size: 48px;
  }

  .prize-name {
    font-size: 20px;
  }
}
</style>

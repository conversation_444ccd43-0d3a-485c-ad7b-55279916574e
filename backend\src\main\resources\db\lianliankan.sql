/*
 Navicat Premium Data Transfer

 Source Server         : login
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : localhost:3306
 Source Schema         : lianliankan

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 31/07/2025 13:03:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for board_item_position
-- ----------------------------
DROP TABLE IF EXISTS `board_item_position`;
CREATE TABLE `board_item_position`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `board_id` bigint NOT NULL COMMENT '棋盘ID',
  `item_id` bigint NOT NULL COMMENT '物品ID',
  `x` int NOT NULL COMMENT 'X坐标',
  `y` int NOT NULL COMMENT 'Y坐标',
  `created_at` datetime(6) NULL DEFAULT NULL,
  `is_removed` bit(1) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `item_id`(`item_id` ASC) USING BTREE,
  INDEX `idx_board_id`(`board_id` ASC) USING BTREE,
  INDEX `idx_position`(`x` ASC, `y` ASC) USING BTREE,
  INDEX `idx_board_position`(`board_id` ASC, `x` ASC, `y` ASC) USING BTREE,
  CONSTRAINT `board_item_position_ibfk_1` FOREIGN KEY (`board_id`) REFERENCES `game_board` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `board_item_position_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `game_item` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2137 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of board_item_position
-- ----------------------------
INSERT INTO `board_item_position` VALUES (531, 1, 18, 0, 0, NULL, b'0');
INSERT INTO `board_item_position` VALUES (532, 1, 18, 7, 6, NULL, b'0');
INSERT INTO `board_item_position` VALUES (533, 1, 19, 3, 0, NULL, b'0');
INSERT INTO `board_item_position` VALUES (2135, 3, 39, 7, 6, '2025-07-28 13:15:32.000000', b'0');
INSERT INTO `board_item_position` VALUES (2136, 3, 31, 9, 10, '2025-07-28 13:15:32.000000', b'0');
INSERT INTO `board_item_position` VALUES (2137, 3, 31, 5, 13, '2025-07-28 13:15:32.000000', b'0');

-- ----------------------------
-- Table structure for board_layout_notes
-- ----------------------------
DROP TABLE IF EXISTS `board_layout_notes`;
CREATE TABLE `board_layout_notes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `board_id` bigint NOT NULL,
  `layout_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `board_id`(`board_id` ASC) USING BTREE,
  CONSTRAINT `board_layout_notes_ibfk_1` FOREIGN KEY (`board_id`) REFERENCES `game_board` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of board_layout_notes
-- ----------------------------
INSERT INTO `board_layout_notes` VALUES (1, 1, '默认棋盘布局说明:\n\n第0行 (y=0):\n- (0,0): 坦克\n- (1,0): 怀表\n- (2,0): 怀表\n- (3,0): 钥匙\n- (4,0): 钥匙\n- (9,0): 装甲车\n\n第2行 (y=2):\n- (0,2): 眼镜\n- (1,2): 眼镜\n- (2,2): 眼镜\n- (3,2): 钥匙\n- (6,2): 眼镜\n\n第4行 (y=4):\n- (0,4): 球\n- (1,4): 怀表\n- (2,4): 怀表\n- (3,4): 怀表\n- (4,4): 怀表\n- (5,4): 钥匙\n- (6,4): 坦克\n\n第6行 (y=6):\n- (9,6): 装甲车\n\n总计19个棋子，包含:\n- 坦克: 2个\n- 怀表: 6个\n- 钥匙: 4个\n- 眼镜: 4个\n- 球: 1个\n- 装甲车: 2个', '2025-07-25 15:51:14');

-- ----------------------------
-- Table structure for board_versions
-- ----------------------------
DROP TABLE IF EXISTS `board_versions`;
CREATE TABLE `board_versions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `version_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本标识',
  `version_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '版本描述',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否为默认版本',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_version_key`(`version_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '棋盘版本配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of board_versions
-- ----------------------------
INSERT INTO `board_versions` VALUES (1, 'normal', '普通版', '标准的连连看棋盘，包含各种复杂物品组合', 0, 1, '2025-07-28 12:11:28', '2025-07-28 12:11:28');
INSERT INTO `board_versions` VALUES (2, 'simple', '简单版', '简化的连连看棋盘，适合新手玩家', 1, 1, '2025-07-28 12:11:28', '2025-07-28 12:11:28');
INSERT INTO `board_versions` VALUES (3, 'hard', '困难版', '15x15大型连连看棋盘，挑战高难度', 0, 1, '2025-07-28 13:15:32', '2025-07-28 13:15:32');

-- ----------------------------
-- Table structure for game_assets
-- ----------------------------
DROP TABLE IF EXISTS `game_assets`;
CREATE TABLE `game_assets`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `game_type_id` bigint NOT NULL COMMENT '游戏类型ID',
  `asset_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型',
  `asset_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源键',
  `asset_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源路径',
  `asset_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '资源名称',
  `file_size` bigint NULL DEFAULT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'MIME类型',
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_game_asset`(`game_type_id` ASC, `asset_key` ASC) USING BTREE,
  CONSTRAINT `game_assets_ibfk_1` FOREIGN KEY (`game_type_id`) REFERENCES `game_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_assets
-- ----------------------------
INSERT INTO `game_assets` VALUES (1, 1, 'image', 'tank', '/images/tank.png', '坦克', NULL, NULL, 1, '2025-07-25 15:27:31');
INSERT INTO `game_assets` VALUES (2, 1, 'image', 'watch', '/images/watch.png', '怀表', NULL, NULL, 1, '2025-07-25 15:27:31');
INSERT INTO `game_assets` VALUES (3, 1, 'image', 'key', '/images/key.png', '钥匙', NULL, NULL, 1, '2025-07-25 15:27:31');
INSERT INTO `game_assets` VALUES (4, 1, 'image', 'glasses', '/images/glasses.png', '眼镜', NULL, NULL, 1, '2025-07-25 15:27:31');
INSERT INTO `game_assets` VALUES (5, 1, 'image', 'ball', '/images/ball.png', '球', NULL, NULL, 1, '2025-07-25 15:27:31');
INSERT INTO `game_assets` VALUES (6, 1, 'image', 'armored_car', '/images/armored_car.png', '装甲车', NULL, NULL, 1, '2025-07-25 15:27:31');

-- ----------------------------
-- Table structure for game_board
-- ----------------------------
DROP TABLE IF EXISTS `game_board`;
CREATE TABLE `game_board`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `height` int NOT NULL,
  `is_default` bit(1) NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `width` int NOT NULL,
  `version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_board
-- ----------------------------
INSERT INTO `game_board` VALUES (1, NULL, 9, b'1', '普通版棋盘', NULL, 10, 'normal');
INSERT INTO `game_board` VALUES (2, '2025-07-28 12:11:28.000000', 6, b'0', '简单版棋盘', '2025-07-28 12:11:28.000000', 6, 'simple');
INSERT INTO `game_board` VALUES (3, '2025-07-28 13:15:32.000000', 15, b'0', '困难版棋盘', '2025-07-28 13:15:32.000000', 15, 'hard');

-- ----------------------------
-- Table structure for game_configs
-- ----------------------------
DROP TABLE IF EXISTS `game_configs`;
CREATE TABLE `game_configs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `game_type_id` bigint NOT NULL COMMENT '游戏类型ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置描述',
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_game_config`(`game_type_id` ASC, `config_key` ASC) USING BTREE,
  CONSTRAINT `game_configs_ibfk_1` FOREIGN KEY (`game_type_id`) REFERENCES `game_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_configs
-- ----------------------------
INSERT INTO `game_configs` VALUES (1, 1, 'default_board', '{\"width\": 10, \"height\": 9}', '默认棋盘配置', 1, '2025-07-25 15:27:31', '2025-07-25 15:27:31');
INSERT INTO `game_configs` VALUES (2, 1, 'scoring_rules', '{\"base_score\": 10, \"large_item_bonus\": 20, \"combo_multiplier\": 1.5}', '得分规则', 1, '2025-07-25 15:27:31', '2025-07-25 15:27:31');
INSERT INTO `game_configs` VALUES (3, 7, 'default_items', '{\"grid_size\": {\"width\": 8, \"height\": 8}, \"total_items\": 54}', '物品选择游戏默认配置', 1, '2025-07-30 00:00:00', '2025-07-30 00:00:00');

-- ----------------------------
-- Table structure for game_item
-- ----------------------------
DROP TABLE IF EXISTS `game_item`;
CREATE TABLE `game_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品名称',
  `image_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片路径',
  `width` int NOT NULL DEFAULT 1 COMMENT '物品宽度',
  `height` int NOT NULL DEFAULT 1 COMMENT '物品高度',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_item
-- ----------------------------
INSERT INTO `game_item` VALUES (18, '坦克', '/images/tank.png', 3, 3, 1, '2025-07-25 17:42:47', '2025-07-25 17:42:47');
INSERT INTO `game_item` VALUES (19, '劳力士', '/images/watch.png', 1, 1, 1, '2025-07-25 17:42:47', '2025-07-25 18:18:39');
INSERT INTO `game_item` VALUES (20, '海洋之泪', '/images/pearl.png', 1, 1, 1, '2025-07-25 17:42:47', '2025-07-25 18:19:13');
INSERT INTO `game_item` VALUES (21, '非洲之心', '/images/diamond.png', 1, 1, 1, '2025-07-25 17:42:47', '2025-07-25 18:19:58');
INSERT INTO `game_item` VALUES (22, '怀表', '/images/pocket_watch.png', 1, 1, 1, '2025-07-25 17:42:47', '2025-07-25 17:42:47');
INSERT INTO `game_item` VALUES (23, '军用终端', '/images/terminal.png', 1, 2, 1, '2025-07-25 17:42:47', '2025-07-30 07:28:26');
INSERT INTO `game_item` VALUES (24, '步战车', '/images/ifv.png', 3, 2, 1, '2025-07-25 17:42:47', '2025-07-25 17:42:47');
INSERT INTO `game_item` VALUES (25, '化石', '/images/fossil.png', 2, 1, 1, '2025-07-25 17:42:47', '2025-07-25 17:59:47');
INSERT INTO `game_item` VALUES (26, '卫星锅', '/images/satellite.png', 2, 2, 1, '2025-07-25 17:42:47', '2025-07-25 17:42:47');
INSERT INTO `game_item` VALUES (27, '量子存储', '/images/usb.png', 1, 1, 1, '2025-07-25 17:42:47', '2025-07-25 18:19:25');
INSERT INTO `game_item` VALUES (28, '滑膛枪', '/images/musket.png', 1, 4, 1, '2025-07-25 17:42:47', '2025-07-25 17:42:47');
INSERT INTO `game_item` VALUES (29, '金条', '/images/gold_bar.png', 1, 2, 1, '2025-07-25 17:42:47', '2025-07-25 17:48:08');
INSERT INTO `game_item` VALUES (30, '实验数据', '/images/cd.png', 1, 1, 1, '2025-07-25 18:00:55', '2025-07-25 18:19:43');
INSERT INTO `game_item` VALUES (31, '瞪羚', '/images/dengling.png', 2, 2, 1, '2025-07-25 18:00:51', '2025-07-25 18:19:43');
INSERT INTO `game_item` VALUES (32, '非洲之心', '/images/diamond.png', 1, 1, 1, '2025-07-28 13:15:32', '2025-07-28 13:38:48');
INSERT INTO `game_item` VALUES (33, '海洋之泪', '/images/pearl.png', 1, 1, 1, '2025-07-28 13:15:32', '2025-07-28 13:43:04');
INSERT INTO `game_item` VALUES (34, '劳力士', '/images/watch.png', 1, 1, 1, '2025-07-28 13:15:32', '2025-07-28 13:39:36');
INSERT INTO `game_item` VALUES (35, '量子存储', '/images/usb.png', 1, 1, 1, '2025-07-28 13:15:32', '2025-07-28 13:39:36');
INSERT INTO `game_item` VALUES (36, '滑膛枪', '/images/gun.png', 1, 4, 1, '2025-07-28 13:15:32', '2025-07-28 13:39:36');
INSERT INTO `game_item` VALUES (37, '刀片服务器', '/images/server.png', 3, 4, 1, '2025-07-28 13:15:32', '2025-07-28 13:43:31');
INSERT INTO `game_item` VALUES (38, '地下金库', '/images/room_card.png', 1, 1, 1, '2025-07-28 13:15:32', '2025-07-28 13:43:18');
INSERT INTO `game_item` VALUES (39, '万金泪冠', '/images/jewelry_box.png', 3, 3, 1, '2025-07-28 13:15:32', '2025-07-28 13:42:51');

-- ----------------------------
-- Table structure for game_progress
-- ----------------------------
DROP TABLE IF EXISTS `game_progress`;
CREATE TABLE `game_progress`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `board_state` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_completed` bit(1) NULL DEFAULT NULL,
  `player_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `progress_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `save_time` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_progress_code`(`progress_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 86 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_progress
-- ----------------------------
INSERT INTO `game_progress` VALUES (81, '{\"gameType\":\"item-selection\",\"playerName\":\"123\",\"items\":[{\"id\":1,\"name\":\"黄金瞪羚\",\"imagePath\":\"/images/黄金瞪羚.png\",\"positionX\":0,\"positionY\":0,\"status\":\"default\"},{\"id\":2,\"name\":\"强化碳纤维板\",\"imagePath\":\"/images/碳纤维板.png\",\"positionX\":1,\"positionY\":0,\"status\":\"default\"},{\"id\":3,\"name\":\"显卡\",\"imagePath\":\"/images/显卡.png\",\"positionX\":2,\"positionY\":0,\"status\":\"default\"},{\"id\":4,\"name\":\"笔记本电脑\",\"imagePath\":\"/images/笔记本.png\",\"positionX\":3,\"positionY\":0,\"status\":\"default\"},{\"id\":5,\"name\":\"军用控制终端\",\"imagePath\":\"/images/小终端.png\",\"positionX\":4,\"positionY\":0,\"status\":\"default\"},{\"id\":6,\"name\":\"刀片服务器\",\"imagePath\":\"/images/刀片服务器.png\",\"positionX\":5,\"positionY\":0,\"status\":\"default\"},{\"id\":7,\"name\":\"呼吸机\",\"imagePath\":\"/images/呼吸机.png\",\"positionX\":6,\"positionY\":0,\"status\":\"confirmed\"},{\"id\":8,\"name\":\"纵横\",\"imagePath\":\"/images/纵横.png\",\"positionX\":7,\"positionY\":0,\"status\":\"default\"},{\"id\":9,\"name\":\"火箭燃料\",\"imagePath\":\"/images/火箭燃油.png\",\"positionX\":0,\"positionY\":1,\"status\":\"default\"},{\"id\":10,\"name\":\"摄影机\",\"imagePath\":\"/images/摄影机.png\",\"positionX\":1,\"positionY\":1,\"status\":\"default\"},{\"id\":11,\"name\":\"便携军用雷达\",\"imagePath\":\"/images/便携军用雷达.png\",\"positionX\":2,\"positionY\":1,\"status\":\"default\"},{\"id\":12,\"name\":\"名贵机械表\",\"imagePath\":\"/images/劳力士.png\",\"positionX\":3,\"positionY\":1,\"status\":\"default\"},{\"id\":13,\"name\":\"扫地机器人\",\"imagePath\":\"/images/扫地机器人.png\",\"positionX\":4,\"positionY\":1,\"status\":\"default\"},{\"id\":14,\"name\":\"挂耳咖啡\",\"imagePath\":\"/images/挂耳咖啡.png\",\"positionX\":5,\"positionY\":1,\"status\":\"default\"},{\"id\":15,\"name\":\"医疗机器人\",\"imagePath\":\"/images/医疗机器人.png\",\"positionX\":6,\"positionY\":1,\"status\":\"confirmed\"},{\"id\":16,\"name\":\"军用电台\",\"imagePath\":\"/images/军用电台.png\",\"positionX\":7,\"positionY\":1,\"status\":\"default\"},{\"id\":17,\"name\":\"无人机\",\"imagePath\":\"/images/无人机.png\",\"positionX\":0,\"positionY\":2,\"status\":\"default\"},{\"id\":18,\"name\":\"军用炮弹\",\"imagePath\":\"/images/军用炮弹.png\",\"positionX\":1,\"positionY\":2,\"status\":\"default\"},{\"id\":19,\"name\":\"金条\",\"imagePath\":\"/images/金条.png\",\"positionX\":2,\"positionY\":2,\"status\":\"default\"},{\"id\":20,\"name\":\"绝密服务器\",\"imagePath\":\"/images/绝密服务器.png\",\"positionX\":3,\"positionY\":2,\"status\":\"default\"},{\"id\":21,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":4,\"positionY\":2,\"status\":\"default\"},{\"id\":22,\"name\":\"鱼子酱\",\"imagePath\":\"/images/鱼子酱.png\",\"positionX\":5,\"positionY\":2,\"status\":\"default\"},{\"id\":23,\"name\":\"微型反应炉\",\"imagePath\":\"/images/微型反应炉.png\",\"positionX\":6,\"positionY\":2,\"status\":\"confirmed\"},{\"id\":24,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":7,\"positionY\":2,\"status\":\"default\"},{\"id\":25,\"name\":\"高速磁盘阵列\",\"imagePath\":\"/images/高速磁盘阵列.png\",\"positionX\":0,\"positionY\":3,\"status\":\"default\"},{\"id\":26,\"name\":\"化石\",\"imagePath\":\"/images/化石.png\",\"positionX\":1,\"positionY\":3,\"status\":\"default\"},{\"id\":27,\"name\":\"主战坦克模型\",\"imagePath\":\"/images/坦克.png\",\"positionX\":2,\"positionY\":3,\"status\":\"default\"},{\"id\":28,\"name\":\"高级咖啡豆\",\"imagePath\":\"/images/咖啡豆.png\",\"positionX\":3,\"positionY\":3,\"status\":\"default\"},{\"id\":29,\"name\":\"强力吸尘器\",\"imagePath\":\"/images/强力吸尘器.png\",\"positionX\":4,\"positionY\":3,\"status\":\"default\"},{\"id\":30,\"name\":\"军用卫星通讯仪\",\"imagePath\":\"/images/军用卫星通讯仪.png\",\"positionX\":5,\"positionY\":3,\"status\":\"default\"},{\"id\":31,\"name\":\"雷斯的留声机\",\"imagePath\":\"/images/雷斯的留声机.png\",\"positionX\":6,\"positionY\":3,\"status\":\"default\"},{\"id\":32,\"name\":\"阵列服务器\",\"imagePath\":\"/images/阵列服务器.png\",\"positionX\":0,\"positionY\":4,\"status\":\"default\"},{\"id\":33,\"name\":\"实验数据\",\"imagePath\":\"/images/实验数据.png\",\"positionX\":1,\"positionY\":4,\"status\":\"default\"},{\"id\":34,\"name\":\"克劳狄乌斯半身像\",\"imagePath\":\"/images/半身像.png\",\"positionX\":2,\"positionY\":4,\"status\":\"default\"},{\"id\":35,\"name\":\"赛伊德的怀表\",\"imagePath\":\"/images/怀表.png\",\"positionX\":3,\"positionY\":4,\"status\":\"default\"},{\"id\":36,\"name\":\"自动体外除颤器\",\"imagePath\":\"/images/自动体外除颤仪.png\",\"positionX\":4,\"positionY\":4,\"status\":\"default\"},{\"id\":37,\"name\":\"奥莉薇娅香槟\",\"imagePath\":\"/images/香槟.png\",\"positionX\":5,\"positionY\":4,\"status\":\"default\"},{\"id\":38,\"name\":\"装甲车电池\",\"imagePath\":\"/images/装甲车电池.png\",\"positionX\":6,\"positionY\":4,\"status\":\"default\"},{\"id\":39,\"name\":\"红房卡\",\"imagePath\":\"/images/红卡.png\",\"positionX\":7,\"positionY\":4,\"status\":\"default\"},{\"id\":40,\"name\":\"飞行记录仪\",\"imagePath\":\"/images/黑匣子.png\",\"positionX\":0,\"positionY\":5,\"status\":\"default\"},{\"id\":41,\"name\":\"滑膛枪展品\",\"imagePath\":\"/images/滑膛枪.png\",\"positionX\":1,\"positionY\":5,\"status\":\"default\"},{\"id\":42,\"name\":\"步战车模型\",\"imagePath\":\"/images/步战车.png\",\"positionX\":2,\"positionY\":5,\"status\":\"default\"},{\"id\":43,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":3,\"positionY\":5,\"status\":\"default\"},{\"id\":44,\"name\":\"复苏呼吸机\",\"imagePath\":\"/images/复苏呼吸机.png\",\"positionX\":4,\"positionY\":5,\"status\":\"default\"},{\"id\":45,\"name\":\"紫房卡\",\"imagePath\":\"/images/紫卡.png\",\"positionX\":5,\"positionY\":5,\"status\":\"default\"},{\"id\":46,\"name\":\"万金泪冠\",\"imagePath\":\"/images/万金泪冠.png\",\"positionX\":6,\"positionY\":5,\"status\":\"default\"},{\"id\":47,\"name\":\"量子存储\",\"imagePath\":\"/images/量子存储.png\",\"positionX\":0,\"positionY\":6,\"status\":\"default\"},{\"id\":48,\"name\":\"曼德尔超算单元\",\"imagePath\":\"/images/超算单元.png\",\"positionX\":1,\"positionY\":6,\"status\":\"default\"},{\"id\":49,\"name\":\"军用信息终端\",\"imagePath\":\"/images/大终端.png\",\"positionX\":2,\"positionY\":6,\"status\":\"default\"},{\"id\":50,\"name\":\"云存储阵列\",\"imagePath\":\"/images/云存储阵列.png\",\"positionX\":3,\"positionY\":6,\"status\":\"default\"},{\"id\":51,\"name\":\"G.T.I卫星通信天线\",\"imagePath\":\"/images/卫星锅.png\",\"positionX\":4,\"positionY\":6,\"status\":\"default\"},{\"id\":52,\"name\":\"动力电池组\",\"imagePath\":\"/images/动力电池组.png\",\"positionX\":5,\"positionY\":6,\"status\":\"default\"},{\"id\":53,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":0,\"positionY\":7,\"status\":\"confirmed\"},{\"id\":54,\"name\":\"炫彩麦小蛋\",\"imagePath\":\"/images/瞪羚.png\",\"positionX\":1,\"positionY\":7,\"status\":\"default\"}],\"version\":\"1.0\",\"timestamp\":\"2025-07-30T15:39:27.685112100\"}', b'0', '123', '184FD7A3AB9D', '2025-07-30 15:39:27.685112');
INSERT INTO `game_progress` VALUES (82, '{\"gameType\":\"item-selection\",\"playerName\":\"123\",\"items\":[{\"id\":1,\"name\":\"黄金瞪羚\",\"imagePath\":\"/images/黄金瞪羚.png\",\"positionX\":0,\"positionY\":0,\"status\":\"default\"},{\"id\":2,\"name\":\"强化碳纤维板\",\"imagePath\":\"/images/碳纤维板.png\",\"positionX\":1,\"positionY\":0,\"status\":\"default\"},{\"id\":3,\"name\":\"显卡\",\"imagePath\":\"/images/显卡.png\",\"positionX\":2,\"positionY\":0,\"status\":\"default\"},{\"id\":4,\"name\":\"笔记本电脑\",\"imagePath\":\"/images/笔记本.png\",\"positionX\":3,\"positionY\":0,\"status\":\"default\"},{\"id\":5,\"name\":\"军用控制终端\",\"imagePath\":\"/images/小终端.png\",\"positionX\":4,\"positionY\":0,\"status\":\"default\"},{\"id\":6,\"name\":\"刀片服务器\",\"imagePath\":\"/images/刀片服务器.png\",\"positionX\":5,\"positionY\":0,\"status\":\"default\"},{\"id\":7,\"name\":\"呼吸机\",\"imagePath\":\"/images/呼吸机.png\",\"positionX\":6,\"positionY\":0,\"status\":\"confirmed\"},{\"id\":8,\"name\":\"纵横\",\"imagePath\":\"/images/纵横.png\",\"positionX\":7,\"positionY\":0,\"status\":\"default\"},{\"id\":9,\"name\":\"火箭燃料\",\"imagePath\":\"/images/火箭燃油.png\",\"positionX\":0,\"positionY\":1,\"status\":\"default\"},{\"id\":10,\"name\":\"摄影机\",\"imagePath\":\"/images/摄影机.png\",\"positionX\":1,\"positionY\":1,\"status\":\"default\"},{\"id\":11,\"name\":\"便携军用雷达\",\"imagePath\":\"/images/便携军用雷达.png\",\"positionX\":2,\"positionY\":1,\"status\":\"default\"},{\"id\":12,\"name\":\"名贵机械表\",\"imagePath\":\"/images/劳力士.png\",\"positionX\":3,\"positionY\":1,\"status\":\"default\"},{\"id\":13,\"name\":\"扫地机器人\",\"imagePath\":\"/images/扫地机器人.png\",\"positionX\":4,\"positionY\":1,\"status\":\"default\"},{\"id\":14,\"name\":\"挂耳咖啡\",\"imagePath\":\"/images/挂耳咖啡.png\",\"positionX\":5,\"positionY\":1,\"status\":\"default\"},{\"id\":15,\"name\":\"医疗机器人\",\"imagePath\":\"/images/医疗机器人.png\",\"positionX\":6,\"positionY\":1,\"status\":\"confirmed\"},{\"id\":16,\"name\":\"军用电台\",\"imagePath\":\"/images/军用电台.png\",\"positionX\":7,\"positionY\":1,\"status\":\"default\"},{\"id\":17,\"name\":\"无人机\",\"imagePath\":\"/images/无人机.png\",\"positionX\":0,\"positionY\":2,\"status\":\"default\"},{\"id\":18,\"name\":\"军用炮弹\",\"imagePath\":\"/images/军用炮弹.png\",\"positionX\":1,\"positionY\":2,\"status\":\"default\"},{\"id\":19,\"name\":\"金条\",\"imagePath\":\"/images/金条.png\",\"positionX\":2,\"positionY\":2,\"status\":\"default\"},{\"id\":20,\"name\":\"绝密服务器\",\"imagePath\":\"/images/绝密服务器.png\",\"positionX\":3,\"positionY\":2,\"status\":\"default\"},{\"id\":21,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":4,\"positionY\":2,\"status\":\"default\"},{\"id\":22,\"name\":\"鱼子酱\",\"imagePath\":\"/images/鱼子酱.png\",\"positionX\":5,\"positionY\":2,\"status\":\"default\"},{\"id\":23,\"name\":\"微型反应炉\",\"imagePath\":\"/images/微型反应炉.png\",\"positionX\":6,\"positionY\":2,\"status\":\"confirmed\"},{\"id\":24,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":7,\"positionY\":2,\"status\":\"default\"},{\"id\":25,\"name\":\"高速磁盘阵列\",\"imagePath\":\"/images/高速磁盘阵列.png\",\"positionX\":0,\"positionY\":3,\"status\":\"default\"},{\"id\":26,\"name\":\"化石\",\"imagePath\":\"/images/化石.png\",\"positionX\":1,\"positionY\":3,\"status\":\"default\"},{\"id\":27,\"name\":\"主战坦克模型\",\"imagePath\":\"/images/坦克.png\",\"positionX\":2,\"positionY\":3,\"status\":\"default\"},{\"id\":28,\"name\":\"高级咖啡豆\",\"imagePath\":\"/images/咖啡豆.png\",\"positionX\":3,\"positionY\":3,\"status\":\"default\"},{\"id\":29,\"name\":\"强力吸尘器\",\"imagePath\":\"/images/强力吸尘器.png\",\"positionX\":4,\"positionY\":3,\"status\":\"default\"},{\"id\":30,\"name\":\"军用卫星通讯仪\",\"imagePath\":\"/images/军用卫星通讯仪.png\",\"positionX\":5,\"positionY\":3,\"status\":\"default\"},{\"id\":31,\"name\":\"雷斯的留声机\",\"imagePath\":\"/images/雷斯的留声机.png\",\"positionX\":6,\"positionY\":3,\"status\":\"default\"},{\"id\":32,\"name\":\"阵列服务器\",\"imagePath\":\"/images/阵列服务器.png\",\"positionX\":0,\"positionY\":4,\"status\":\"default\"},{\"id\":33,\"name\":\"实验数据\",\"imagePath\":\"/images/实验数据.png\",\"positionX\":1,\"positionY\":4,\"status\":\"default\"},{\"id\":34,\"name\":\"克劳狄乌斯半身像\",\"imagePath\":\"/images/半身像.png\",\"positionX\":2,\"positionY\":4,\"status\":\"default\"},{\"id\":35,\"name\":\"赛伊德的怀表\",\"imagePath\":\"/images/怀表.png\",\"positionX\":3,\"positionY\":4,\"status\":\"default\"},{\"id\":36,\"name\":\"自动体外除颤器\",\"imagePath\":\"/images/自动体外除颤仪.png\",\"positionX\":4,\"positionY\":4,\"status\":\"default\"},{\"id\":37,\"name\":\"奥莉薇娅香槟\",\"imagePath\":\"/images/香槟.png\",\"positionX\":5,\"positionY\":4,\"status\":\"default\"},{\"id\":38,\"name\":\"装甲车电池\",\"imagePath\":\"/images/装甲车电池.png\",\"positionX\":6,\"positionY\":4,\"status\":\"default\"},{\"id\":39,\"name\":\"红房卡\",\"imagePath\":\"/images/红卡.png\",\"positionX\":7,\"positionY\":4,\"status\":\"default\"},{\"id\":40,\"name\":\"飞行记录仪\",\"imagePath\":\"/images/黑匣子.png\",\"positionX\":0,\"positionY\":5,\"status\":\"default\"},{\"id\":41,\"name\":\"滑膛枪展品\",\"imagePath\":\"/images/滑膛枪.png\",\"positionX\":1,\"positionY\":5,\"status\":\"default\"},{\"id\":42,\"name\":\"步战车模型\",\"imagePath\":\"/images/步战车.png\",\"positionX\":2,\"positionY\":5,\"status\":\"default\"},{\"id\":43,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":3,\"positionY\":5,\"status\":\"default\"},{\"id\":44,\"name\":\"复苏呼吸机\",\"imagePath\":\"/images/复苏呼吸机.png\",\"positionX\":4,\"positionY\":5,\"status\":\"confirmed\"},{\"id\":45,\"name\":\"紫房卡\",\"imagePath\":\"/images/紫卡.png\",\"positionX\":5,\"positionY\":5,\"status\":\"confirmed\"},{\"id\":46,\"name\":\"万金泪冠\",\"imagePath\":\"/images/万金泪冠.png\",\"positionX\":6,\"positionY\":5,\"status\":\"default\"},{\"id\":47,\"name\":\"量子存储\",\"imagePath\":\"/images/量子存储.png\",\"positionX\":0,\"positionY\":6,\"status\":\"default\"},{\"id\":48,\"name\":\"曼德尔超算单元\",\"imagePath\":\"/images/超算单元.png\",\"positionX\":1,\"positionY\":6,\"status\":\"default\"},{\"id\":49,\"name\":\"军用信息终端\",\"imagePath\":\"/images/大终端.png\",\"positionX\":2,\"positionY\":6,\"status\":\"default\"},{\"id\":50,\"name\":\"云存储阵列\",\"imagePath\":\"/images/云存储阵列.png\",\"positionX\":3,\"positionY\":6,\"status\":\"default\"},{\"id\":51,\"name\":\"G.T.I卫星通信天线\",\"imagePath\":\"/images/卫星锅.png\",\"positionX\":4,\"positionY\":6,\"status\":\"default\"},{\"id\":52,\"name\":\"动力电池组\",\"imagePath\":\"/images/动力电池组.png\",\"positionX\":5,\"positionY\":6,\"status\":\"default\"},{\"id\":53,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":0,\"positionY\":7,\"status\":\"confirmed\"},{\"id\":54,\"name\":\"炫彩麦小蛋\",\"imagePath\":\"/images/瞪羚.png\",\"positionX\":1,\"positionY\":7,\"status\":\"default\"}],\"version\":\"1.0\",\"timestamp\":\"2025-07-30T15:40:09.514737\"}', b'0', '123', 'B366FC1D23C9', '2025-07-30 15:40:09.514737');
INSERT INTO `game_progress` VALUES (83, '{\"gameType\":\"item-selection\",\"playerName\":\"123\",\"items\":[{\"id\":1,\"name\":\"黄金瞪羚\",\"imagePath\":\"/images/黄金瞪羚.png\",\"positionX\":0,\"positionY\":0,\"status\":\"default\"},{\"id\":2,\"name\":\"强化碳纤维板\",\"imagePath\":\"/images/碳纤维板.png\",\"positionX\":1,\"positionY\":0,\"status\":\"default\"},{\"id\":3,\"name\":\"显卡\",\"imagePath\":\"/images/显卡.png\",\"positionX\":2,\"positionY\":0,\"status\":\"default\"},{\"id\":4,\"name\":\"笔记本电脑\",\"imagePath\":\"/images/笔记本.png\",\"positionX\":3,\"positionY\":0,\"status\":\"default\"},{\"id\":5,\"name\":\"军用控制终端\",\"imagePath\":\"/images/小终端.png\",\"positionX\":4,\"positionY\":0,\"status\":\"default\"},{\"id\":6,\"name\":\"刀片服务器\",\"imagePath\":\"/images/刀片服务器.png\",\"positionX\":5,\"positionY\":0,\"status\":\"default\"},{\"id\":7,\"name\":\"呼吸机\",\"imagePath\":\"/images/呼吸机.png\",\"positionX\":6,\"positionY\":0,\"status\":\"confirmed\"},{\"id\":8,\"name\":\"纵横\",\"imagePath\":\"/images/纵横.png\",\"positionX\":7,\"positionY\":0,\"status\":\"default\"},{\"id\":9,\"name\":\"火箭燃料\",\"imagePath\":\"/images/火箭燃油.png\",\"positionX\":0,\"positionY\":1,\"status\":\"default\"},{\"id\":10,\"name\":\"摄影机\",\"imagePath\":\"/images/摄影机.png\",\"positionX\":1,\"positionY\":1,\"status\":\"default\"},{\"id\":11,\"name\":\"便携军用雷达\",\"imagePath\":\"/images/便携军用雷达.png\",\"positionX\":2,\"positionY\":1,\"status\":\"default\"},{\"id\":12,\"name\":\"名贵机械表\",\"imagePath\":\"/images/劳力士.png\",\"positionX\":3,\"positionY\":1,\"status\":\"default\"},{\"id\":13,\"name\":\"扫地机器人\",\"imagePath\":\"/images/扫地机器人.png\",\"positionX\":4,\"positionY\":1,\"status\":\"default\"},{\"id\":14,\"name\":\"挂耳咖啡\",\"imagePath\":\"/images/挂耳咖啡.png\",\"positionX\":5,\"positionY\":1,\"status\":\"default\"},{\"id\":15,\"name\":\"医疗机器人\",\"imagePath\":\"/images/医疗机器人.png\",\"positionX\":6,\"positionY\":1,\"status\":\"confirmed\"},{\"id\":16,\"name\":\"军用电台\",\"imagePath\":\"/images/军用电台.png\",\"positionX\":7,\"positionY\":1,\"status\":\"default\"},{\"id\":17,\"name\":\"无人机\",\"imagePath\":\"/images/无人机.png\",\"positionX\":0,\"positionY\":2,\"status\":\"default\"},{\"id\":18,\"name\":\"军用炮弹\",\"imagePath\":\"/images/军用炮弹.png\",\"positionX\":1,\"positionY\":2,\"status\":\"default\"},{\"id\":19,\"name\":\"金条\",\"imagePath\":\"/images/金条.png\",\"positionX\":2,\"positionY\":2,\"status\":\"default\"},{\"id\":20,\"name\":\"绝密服务器\",\"imagePath\":\"/images/绝密服务器.png\",\"positionX\":3,\"positionY\":2,\"status\":\"default\"},{\"id\":21,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":4,\"positionY\":2,\"status\":\"default\"},{\"id\":22,\"name\":\"鱼子酱\",\"imagePath\":\"/images/鱼子酱.png\",\"positionX\":5,\"positionY\":2,\"status\":\"default\"},{\"id\":23,\"name\":\"微型反应炉\",\"imagePath\":\"/images/微型反应炉.png\",\"positionX\":6,\"positionY\":2,\"status\":\"confirmed\"},{\"id\":24,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":7,\"positionY\":2,\"status\":\"default\"},{\"id\":25,\"name\":\"高速磁盘阵列\",\"imagePath\":\"/images/高速磁盘阵列.png\",\"positionX\":0,\"positionY\":3,\"status\":\"default\"},{\"id\":26,\"name\":\"化石\",\"imagePath\":\"/images/化石.png\",\"positionX\":1,\"positionY\":3,\"status\":\"default\"},{\"id\":27,\"name\":\"主战坦克模型\",\"imagePath\":\"/images/坦克.png\",\"positionX\":2,\"positionY\":3,\"status\":\"default\"},{\"id\":28,\"name\":\"高级咖啡豆\",\"imagePath\":\"/images/咖啡豆.png\",\"positionX\":3,\"positionY\":3,\"status\":\"default\"},{\"id\":29,\"name\":\"强力吸尘器\",\"imagePath\":\"/images/强力吸尘器.png\",\"positionX\":4,\"positionY\":3,\"status\":\"default\"},{\"id\":30,\"name\":\"军用卫星通讯仪\",\"imagePath\":\"/images/军用卫星通讯仪.png\",\"positionX\":5,\"positionY\":3,\"status\":\"default\"},{\"id\":31,\"name\":\"雷斯的留声机\",\"imagePath\":\"/images/雷斯的留声机.png\",\"positionX\":6,\"positionY\":3,\"status\":\"default\"},{\"id\":32,\"name\":\"阵列服务器\",\"imagePath\":\"/images/阵列服务器.png\",\"positionX\":0,\"positionY\":4,\"status\":\"default\"},{\"id\":33,\"name\":\"实验数据\",\"imagePath\":\"/images/实验数据.png\",\"positionX\":1,\"positionY\":4,\"status\":\"default\"},{\"id\":34,\"name\":\"克劳狄乌斯半身像\",\"imagePath\":\"/images/半身像.png\",\"positionX\":2,\"positionY\":4,\"status\":\"default\"},{\"id\":35,\"name\":\"赛伊德的怀表\",\"imagePath\":\"/images/怀表.png\",\"positionX\":3,\"positionY\":4,\"status\":\"default\"},{\"id\":36,\"name\":\"自动体外除颤器\",\"imagePath\":\"/images/自动体外除颤仪.png\",\"positionX\":4,\"positionY\":4,\"status\":\"default\"},{\"id\":37,\"name\":\"奥莉薇娅香槟\",\"imagePath\":\"/images/香槟.png\",\"positionX\":5,\"positionY\":4,\"status\":\"default\"},{\"id\":38,\"name\":\"装甲车电池\",\"imagePath\":\"/images/装甲车电池.png\",\"positionX\":6,\"positionY\":4,\"status\":\"default\"},{\"id\":39,\"name\":\"红房卡\",\"imagePath\":\"/images/红卡.png\",\"positionX\":7,\"positionY\":4,\"status\":\"default\"},{\"id\":40,\"name\":\"飞行记录仪\",\"imagePath\":\"/images/黑匣子.png\",\"positionX\":0,\"positionY\":5,\"status\":\"default\"},{\"id\":41,\"name\":\"滑膛枪展品\",\"imagePath\":\"/images/滑膛枪.png\",\"positionX\":1,\"positionY\":5,\"status\":\"default\"},{\"id\":42,\"name\":\"步战车模型\",\"imagePath\":\"/images/步战车.png\",\"positionX\":2,\"positionY\":5,\"status\":\"default\"},{\"id\":43,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":3,\"positionY\":5,\"status\":\"default\"},{\"id\":44,\"name\":\"复苏呼吸机\",\"imagePath\":\"/images/复苏呼吸机.png\",\"positionX\":4,\"positionY\":5,\"status\":\"confirmed\"},{\"id\":45,\"name\":\"紫房卡\",\"imagePath\":\"/images/紫卡.png\",\"positionX\":5,\"positionY\":5,\"status\":\"confirmed\"},{\"id\":46,\"name\":\"万金泪冠\",\"imagePath\":\"/images/万金泪冠.png\",\"positionX\":6,\"positionY\":5,\"status\":\"default\"},{\"id\":47,\"name\":\"量子存储\",\"imagePath\":\"/images/量子存储.png\",\"positionX\":0,\"positionY\":6,\"status\":\"default\"},{\"id\":48,\"name\":\"曼德尔超算单元\",\"imagePath\":\"/images/超算单元.png\",\"positionX\":1,\"positionY\":6,\"status\":\"default\"},{\"id\":49,\"name\":\"军用信息终端\",\"imagePath\":\"/images/大终端.png\",\"positionX\":2,\"positionY\":6,\"status\":\"default\"},{\"id\":50,\"name\":\"云存储阵列\",\"imagePath\":\"/images/云存储阵列.png\",\"positionX\":3,\"positionY\":6,\"status\":\"default\"},{\"id\":51,\"name\":\"G.T.I卫星通信天线\",\"imagePath\":\"/images/卫星锅.png\",\"positionX\":4,\"positionY\":6,\"status\":\"default\"},{\"id\":52,\"name\":\"动力电池组\",\"imagePath\":\"/images/动力电池组.png\",\"positionX\":5,\"positionY\":6,\"status\":\"default\"},{\"id\":53,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":0,\"positionY\":7,\"status\":\"confirmed\"},{\"id\":54,\"name\":\"炫彩麦小蛋\",\"imagePath\":\"/images/瞪羚.png\",\"positionX\":1,\"positionY\":7,\"status\":\"default\"}],\"version\":\"1.0\",\"timestamp\":\"2025-07-30T15:40:52.912228600\"}', b'0', '123', '93A6D7E2824E', '2025-07-30 15:40:52.912229');
INSERT INTO `game_progress` VALUES (84, '{\"boardId\":3,\"width\":15,\"height\":15,\"items\":[{\"id\":2000,\"itemId\":24,\"name\":\"步战车\",\"x\":8,\"y\":1,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2001,\"itemId\":24,\"name\":\"步战车\",\"x\":11,\"y\":7,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2002,\"itemId\":24,\"name\":\"步战车\",\"x\":4,\"y\":9,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2003,\"itemId\":24,\"name\":\"步战车\",\"x\":12,\"y\":13,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2004,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2005,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2006,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2007,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2008,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2009,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2010,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2011,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2012,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2013,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2014,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2015,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2016,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2017,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2018,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2019,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2020,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2021,\"itemId\":32,\"name\":\"非洲之心\",\"x\":9,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2022,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2023,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2024,\"itemId\":32,\"name\":\"非洲之心\",\"x\":8,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2025,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2026,\"itemId\":25,\"name\":\"化石\",\"x\":6,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2027,\"itemId\":25,\"name\":\"化石\",\"x\":10,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2028,\"itemId\":25,\"name\":\"化石\",\"x\":0,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2029,\"itemId\":25,\"name\":\"化石\",\"x\":5,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2030,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2031,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2032,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2033,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2034,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2035,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2036,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2037,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2038,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2039,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2040,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":6,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2041,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2042,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2043,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2044,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":8,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2045,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":9,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2046,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":10,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2047,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2048,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2049,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2050,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2051,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2052,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2053,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2054,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2055,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2056,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2057,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2058,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2059,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2060,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2061,\"itemId\":22,\"name\":\"怀表\",\"x\":5,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2062,\"itemId\":22,\"name\":\"怀表\",\"x\":7,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2063,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2064,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2065,\"itemId\":22,\"name\":\"怀表\",\"x\":9,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2066,\"itemId\":22,\"name\":\"怀表\",\"x\":10,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2067,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2068,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2069,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2070,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2071,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2072,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2073,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2074,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2075,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2076,\"itemId\":34,\"name\":\"劳力士\",\"x\":1,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2077,\"itemId\":34,\"name\":\"劳力士\",\"x\":3,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2078,\"itemId\":34,\"name\":\"劳力士\",\"x\":4,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2079,\"itemId\":34,\"name\":\"劳力士\",\"x\":5,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2080,\"itemId\":34,\"name\":\"劳力士\",\"x\":8,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2081,\"itemId\":34,\"name\":\"劳力士\",\"x\":9,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2082,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2083,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2084,\"itemId\":34,\"name\":\"劳力士\",\"x\":12,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2085,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2086,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2087,\"itemId\":34,\"name\":\"劳力士\",\"x\":14,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2088,\"itemId\":35,\"name\":\"量子存储\",\"x\":3,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2089,\"itemId\":35,\"name\":\"量子存储\",\"x\":4,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2090,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2091,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2092,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2093,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2094,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2095,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2096,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2097,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2098,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2099,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2100,\"itemId\":35,\"name\":\"量子存储\",\"x\":11,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2101,\"itemId\":35,\"name\":\"量子存储\",\"x\":12,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2102,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2103,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2104,\"itemId\":29,\"name\":\"金条\",\"x\":3,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2105,\"itemId\":29,\"name\":\"金条\",\"x\":4,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2106,\"itemId\":29,\"name\":\"金条\",\"x\":6,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2107,\"itemId\":29,\"name\":\"金条\",\"x\":10,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2108,\"itemId\":26,\"name\":\"卫星锅\",\"x\":0,\"y\":0,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2109,\"itemId\":26,\"name\":\"卫星锅\",\"x\":5,\"y\":6,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2110,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":5,\"y\":0,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2111,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":11,\"y\":9,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2112,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":12,\"y\":0,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2113,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":0,\"y\":11,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2114,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2115,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2116,\"itemId\":38,\"name\":\"地下金库\",\"x\":1,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2117,\"itemId\":38,\"name\":\"地下金库\",\"x\":2,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2118,\"itemId\":38,\"name\":\"地下金库\",\"x\":3,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2119,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2120,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2121,\"itemId\":38,\"name\":\"地下金库\",\"x\":7,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2122,\"itemId\":38,\"name\":\"地下金库\",\"x\":8,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2123,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2124,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2125,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2126,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2127,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2128,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2129,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2130,\"itemId\":38,\"name\":\"地下金库\",\"x\":13,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2131,\"itemId\":38,\"name\":\"地下金库\",\"x\":14,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2132,\"itemId\":23,\"name\":\"军用终端\",\"x\":3,\"y\":13,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2133,\"itemId\":23,\"name\":\"军用终端\",\"x\":8,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2134,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":2,\"y\":1,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2135,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":7,\"y\":6,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2136,\"itemId\":31,\"name\":\"瞪羚\",\"x\":9,\"y\":10,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false},{\"id\":2137,\"itemId\":31,\"name\":\"瞪羚\",\"x\":5,\"y\":13,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false}],\"storedRed\":{\"red6\":0,\"red9\":0,\"red12\":0}}', b'0', NULL, '0CBA27BB5229', '2025-07-30 15:42:09.700051');
INSERT INTO `game_progress` VALUES (85, '{\"boardId\":3,\"width\":15,\"height\":15,\"items\":[{\"id\":2000,\"itemId\":24,\"name\":\"步战车\",\"x\":8,\"y\":1,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2001,\"itemId\":24,\"name\":\"步战车\",\"x\":11,\"y\":7,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2002,\"itemId\":24,\"name\":\"步战车\",\"x\":4,\"y\":9,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2003,\"itemId\":24,\"name\":\"步战车\",\"x\":12,\"y\":13,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2004,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2005,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2006,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2007,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2008,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2009,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2010,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2011,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2012,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2013,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2014,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2015,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2016,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2017,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2018,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2019,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2020,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2021,\"itemId\":32,\"name\":\"非洲之心\",\"x\":9,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2022,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2023,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2024,\"itemId\":32,\"name\":\"非洲之心\",\"x\":8,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2025,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2026,\"itemId\":25,\"name\":\"化石\",\"x\":6,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2027,\"itemId\":25,\"name\":\"化石\",\"x\":10,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2028,\"itemId\":25,\"name\":\"化石\",\"x\":0,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2029,\"itemId\":25,\"name\":\"化石\",\"x\":5,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2030,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2031,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2032,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2033,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2034,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2035,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2036,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2037,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2038,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2039,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2040,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":6,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2041,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2042,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2043,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2044,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":8,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2045,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":9,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2046,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":10,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2047,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2048,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2049,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2050,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2051,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2052,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2053,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2054,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2055,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2056,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2057,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2058,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2059,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2060,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2061,\"itemId\":22,\"name\":\"怀表\",\"x\":5,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2062,\"itemId\":22,\"name\":\"怀表\",\"x\":7,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2063,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2064,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2065,\"itemId\":22,\"name\":\"怀表\",\"x\":9,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2066,\"itemId\":22,\"name\":\"怀表\",\"x\":10,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2067,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2068,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2069,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2070,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2071,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2072,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2073,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2074,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2075,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2076,\"itemId\":34,\"name\":\"劳力士\",\"x\":1,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2077,\"itemId\":34,\"name\":\"劳力士\",\"x\":3,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2078,\"itemId\":34,\"name\":\"劳力士\",\"x\":4,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2079,\"itemId\":34,\"name\":\"劳力士\",\"x\":5,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2080,\"itemId\":34,\"name\":\"劳力士\",\"x\":8,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2081,\"itemId\":34,\"name\":\"劳力士\",\"x\":9,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2082,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2083,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2084,\"itemId\":34,\"name\":\"劳力士\",\"x\":12,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2085,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2086,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2087,\"itemId\":34,\"name\":\"劳力士\",\"x\":14,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2088,\"itemId\":35,\"name\":\"量子存储\",\"x\":3,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2089,\"itemId\":35,\"name\":\"量子存储\",\"x\":4,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2090,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2091,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2092,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2093,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2094,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2095,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2096,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2097,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2098,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2099,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2100,\"itemId\":35,\"name\":\"量子存储\",\"x\":11,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2101,\"itemId\":35,\"name\":\"量子存储\",\"x\":12,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2102,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2103,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2104,\"itemId\":29,\"name\":\"金条\",\"x\":3,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2105,\"itemId\":29,\"name\":\"金条\",\"x\":4,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2106,\"itemId\":29,\"name\":\"金条\",\"x\":6,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2107,\"itemId\":29,\"name\":\"金条\",\"x\":10,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2108,\"itemId\":26,\"name\":\"卫星锅\",\"x\":0,\"y\":0,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2109,\"itemId\":26,\"name\":\"卫星锅\",\"x\":5,\"y\":6,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2110,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":5,\"y\":0,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2111,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":11,\"y\":9,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2112,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":12,\"y\":0,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2113,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":0,\"y\":11,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2114,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2115,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2116,\"itemId\":38,\"name\":\"地下金库\",\"x\":1,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2117,\"itemId\":38,\"name\":\"地下金库\",\"x\":2,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2118,\"itemId\":38,\"name\":\"地下金库\",\"x\":3,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2119,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2120,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2121,\"itemId\":38,\"name\":\"地下金库\",\"x\":7,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2122,\"itemId\":38,\"name\":\"地下金库\",\"x\":8,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2123,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2124,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2125,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2126,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2127,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2128,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2129,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2130,\"itemId\":38,\"name\":\"地下金库\",\"x\":13,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2131,\"itemId\":38,\"name\":\"地下金库\",\"x\":14,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2132,\"itemId\":23,\"name\":\"军用终端\",\"x\":3,\"y\":13,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2133,\"itemId\":23,\"name\":\"军用终端\",\"x\":8,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2134,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":2,\"y\":1,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2135,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":7,\"y\":6,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2136,\"itemId\":31,\"name\":\"瞪羚\",\"x\":9,\"y\":10,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false},{\"id\":2137,\"itemId\":31,\"name\":\"瞪羚\",\"x\":5,\"y\":13,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false}],\"storedRed\":{\"red6\":0,\"red9\":0,\"red12\":0}}', b'0', NULL, '30AE227471D3', '2025-07-30 15:42:13.752717');
INSERT INTO `game_progress` VALUES (86, '{\"boardId\":3,\"width\":15,\"height\":15,\"items\":[{\"id\":2000,\"itemId\":24,\"name\":\"步战车\",\"x\":8,\"y\":1,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2001,\"itemId\":24,\"name\":\"步战车\",\"x\":11,\"y\":7,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2002,\"itemId\":24,\"name\":\"步战车\",\"x\":4,\"y\":9,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2003,\"itemId\":24,\"name\":\"步战车\",\"x\":12,\"y\":13,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":2004,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2005,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2006,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2007,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2008,\"itemId\":32,\"name\":\"非洲之心\",\"x\":0,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2009,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2010,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2011,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2012,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2013,\"itemId\":32,\"name\":\"非洲之心\",\"x\":1,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2014,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2015,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2016,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2017,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2018,\"itemId\":32,\"name\":\"非洲之心\",\"x\":2,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2019,\"itemId\":32,\"name\":\"非洲之心\",\"x\":13,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2020,\"itemId\":32,\"name\":\"非洲之心\",\"x\":7,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2021,\"itemId\":32,\"name\":\"非洲之心\",\"x\":9,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2022,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2023,\"itemId\":32,\"name\":\"非洲之心\",\"x\":4,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2024,\"itemId\":32,\"name\":\"非洲之心\",\"x\":8,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2025,\"itemId\":32,\"name\":\"非洲之心\",\"x\":10,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":2026,\"itemId\":25,\"name\":\"化石\",\"x\":6,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2027,\"itemId\":25,\"name\":\"化石\",\"x\":10,\"y\":0,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2028,\"itemId\":25,\"name\":\"化石\",\"x\":0,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2029,\"itemId\":25,\"name\":\"化石\",\"x\":5,\"y\":8,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":2030,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2031,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2032,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2033,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":1,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2034,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2035,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":2,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2036,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2037,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":3,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2038,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2039,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":4,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2040,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":6,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2041,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2042,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2043,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":7,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2044,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":8,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2045,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":9,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2046,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":10,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2047,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2048,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2049,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":11,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2050,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2051,\"itemId\":33,\"name\":\"海洋之泪\",\"x\":14,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":2052,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2053,\"itemId\":22,\"name\":\"怀表\",\"x\":0,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2054,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2055,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2056,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2057,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2058,\"itemId\":22,\"name\":\"怀表\",\"x\":3,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2059,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2060,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2061,\"itemId\":22,\"name\":\"怀表\",\"x\":5,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":true},{\"id\":2062,\"itemId\":22,\"name\":\"怀表\",\"x\":7,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2063,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2064,\"itemId\":22,\"name\":\"怀表\",\"x\":8,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2065,\"itemId\":22,\"name\":\"怀表\",\"x\":9,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2066,\"itemId\":22,\"name\":\"怀表\",\"x\":10,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2067,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2068,\"itemId\":22,\"name\":\"怀表\",\"x\":11,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2069,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2070,\"itemId\":22,\"name\":\"怀表\",\"x\":12,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2071,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2072,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2073,\"itemId\":22,\"name\":\"怀表\",\"x\":14,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":2074,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2075,\"itemId\":34,\"name\":\"劳力士\",\"x\":0,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2076,\"itemId\":34,\"name\":\"劳力士\",\"x\":1,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2077,\"itemId\":34,\"name\":\"劳力士\",\"x\":3,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2078,\"itemId\":34,\"name\":\"劳力士\",\"x\":4,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2079,\"itemId\":34,\"name\":\"劳力士\",\"x\":5,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2080,\"itemId\":34,\"name\":\"劳力士\",\"x\":8,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2081,\"itemId\":34,\"name\":\"劳力士\",\"x\":9,\"y\":14,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2082,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2083,\"itemId\":34,\"name\":\"劳力士\",\"x\":10,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2084,\"itemId\":34,\"name\":\"劳力士\",\"x\":12,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2085,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2086,\"itemId\":34,\"name\":\"劳力士\",\"x\":13,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2087,\"itemId\":34,\"name\":\"劳力士\",\"x\":14,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":2088,\"itemId\":35,\"name\":\"量子存储\",\"x\":3,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2089,\"itemId\":35,\"name\":\"量子存储\",\"x\":4,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2090,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2091,\"itemId\":35,\"name\":\"量子存储\",\"x\":5,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2092,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2093,\"itemId\":35,\"name\":\"量子存储\",\"x\":6,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2094,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2095,\"itemId\":35,\"name\":\"量子存储\",\"x\":7,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2096,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2097,\"itemId\":35,\"name\":\"量子存储\",\"x\":8,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2098,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2099,\"itemId\":35,\"name\":\"量子存储\",\"x\":9,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2100,\"itemId\":35,\"name\":\"量子存储\",\"x\":11,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2101,\"itemId\":35,\"name\":\"量子存储\",\"x\":12,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2102,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2103,\"itemId\":35,\"name\":\"量子存储\",\"x\":14,\"y\":12,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":2104,\"itemId\":29,\"name\":\"金条\",\"x\":3,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2105,\"itemId\":29,\"name\":\"金条\",\"x\":4,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2106,\"itemId\":29,\"name\":\"金条\",\"x\":6,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2107,\"itemId\":29,\"name\":\"金条\",\"x\":10,\"y\":4,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":2108,\"itemId\":26,\"name\":\"卫星锅\",\"x\":0,\"y\":0,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2109,\"itemId\":26,\"name\":\"卫星锅\",\"x\":5,\"y\":6,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":2110,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":5,\"y\":0,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2111,\"itemId\":36,\"name\":\"滑膛枪\",\"x\":11,\"y\":9,\"width\":1,\"height\":4,\"imagePath\":\"/images/gun.png\",\"isRemoved\":false},{\"id\":2112,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":12,\"y\":0,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2113,\"itemId\":37,\"name\":\"刀片服务器\",\"x\":0,\"y\":11,\"width\":3,\"height\":4,\"imagePath\":\"/images/server.png\",\"isRemoved\":false},{\"id\":2114,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2115,\"itemId\":38,\"name\":\"地下金库\",\"x\":0,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2116,\"itemId\":38,\"name\":\"地下金库\",\"x\":1,\"y\":9,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2117,\"itemId\":38,\"name\":\"地下金库\",\"x\":2,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2118,\"itemId\":38,\"name\":\"地下金库\",\"x\":3,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2119,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2120,\"itemId\":38,\"name\":\"地下金库\",\"x\":6,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2121,\"itemId\":38,\"name\":\"地下金库\",\"x\":7,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2122,\"itemId\":38,\"name\":\"地下金库\",\"x\":8,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2123,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2124,\"itemId\":38,\"name\":\"地下金库\",\"x\":9,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2125,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2126,\"itemId\":38,\"name\":\"地下金库\",\"x\":11,\"y\":13,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2127,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2128,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":10,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2129,\"itemId\":38,\"name\":\"地下金库\",\"x\":12,\"y\":11,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2130,\"itemId\":38,\"name\":\"地下金库\",\"x\":13,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2131,\"itemId\":38,\"name\":\"地下金库\",\"x\":14,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/room_card.png\",\"isRemoved\":false},{\"id\":2132,\"itemId\":23,\"name\":\"军用终端\",\"x\":3,\"y\":13,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2133,\"itemId\":23,\"name\":\"军用终端\",\"x\":8,\"y\":11,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":2134,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":2,\"y\":1,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2135,\"itemId\":39,\"name\":\"万金泪冠\",\"x\":7,\"y\":6,\"width\":3,\"height\":3,\"imagePath\":\"/images/jewelry_box.png\",\"isRemoved\":false},{\"id\":2136,\"itemId\":31,\"name\":\"瞪羚\",\"x\":9,\"y\":10,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false},{\"id\":2137,\"itemId\":31,\"name\":\"瞪羚\",\"x\":5,\"y\":13,\"width\":2,\"height\":2,\"imagePath\":\"/images/dengling.png\",\"isRemoved\":false}],\"storedRed\":{\"red6\":0,\"red9\":0,\"red12\":0}}', b'0', NULL, 'ACC0775A27B0', '2025-07-30 15:42:21.422440');

-- ----------------------------
-- Table structure for game_sessions
-- ----------------------------
DROP TABLE IF EXISTS `game_sessions`;
CREATE TABLE `game_sessions`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `end_time` datetime(6) NULL DEFAULT NULL,
  `game_state` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `game_type_id` bigint NOT NULL,
  `is_completed` bit(1) NULL DEFAULT NULL,
  `last_update_time` datetime(6) NULL DEFAULT NULL,
  `level_or_stage` int NULL DEFAULT NULL,
  `player_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `score` int NULL DEFAULT NULL,
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `start_time` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_2k3fih3p9q6jqrjy3wst3td13`(`session_id` ASC) USING BTREE,
  INDEX `idx_session_id`(`session_id` ASC) USING BTREE,
  INDEX `idx_game_type`(`game_type_id` ASC) USING BTREE,
  CONSTRAINT `FK53xjiet44ue2y7063d5h04qlj` FOREIGN KEY (`game_type_id`) REFERENCES `game_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `game_sessions_ibfk_1` FOREIGN KEY (`game_type_id`) REFERENCES `game_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 165 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_sessions
-- ----------------------------
INSERT INTO `game_sessions` VALUES (161, NULL, '[{\"id\":1,\"name\":\"黄金瞪羚\",\"imagePath\":\"/images/黄金瞪羚.png\",\"positionX\":0,\"positionY\":0,\"status\":\"default\"},{\"id\":2,\"name\":\"强化碳纤维板\",\"imagePath\":\"/images/碳纤维板.png\",\"positionX\":1,\"positionY\":0,\"status\":\"default\"},{\"id\":3,\"name\":\"显卡\",\"imagePath\":\"/images/显卡.png\",\"positionX\":2,\"positionY\":0,\"status\":\"default\"},{\"id\":4,\"name\":\"笔记本电脑\",\"imagePath\":\"/images/笔记本.png\",\"positionX\":3,\"positionY\":0,\"status\":\"default\"},{\"id\":5,\"name\":\"军用控制终端\",\"imagePath\":\"/images/小终端.png\",\"positionX\":4,\"positionY\":0,\"status\":\"default\"},{\"id\":6,\"name\":\"刀片服务器\",\"imagePath\":\"/images/刀片服务器.png\",\"positionX\":5,\"positionY\":0,\"status\":\"default\"},{\"id\":7,\"name\":\"呼吸机\",\"imagePath\":\"/images/呼吸机.png\",\"positionX\":6,\"positionY\":0,\"status\":\"default\"},{\"id\":8,\"name\":\"纵横\",\"imagePath\":\"/images/纵横.png\",\"positionX\":7,\"positionY\":0,\"status\":\"default\"},{\"id\":9,\"name\":\"火箭燃料\",\"imagePath\":\"/images/火箭燃油.png\",\"positionX\":0,\"positionY\":1,\"status\":\"default\"},{\"id\":10,\"name\":\"摄影机\",\"imagePath\":\"/images/摄影机.png\",\"positionX\":1,\"positionY\":1,\"status\":\"default\"},{\"id\":11,\"name\":\"便携军用雷达\",\"imagePath\":\"/images/便携军用雷达.png\",\"positionX\":2,\"positionY\":1,\"status\":\"default\"},{\"id\":12,\"name\":\"名贵机械表\",\"imagePath\":\"/images/劳力士.png\",\"positionX\":3,\"positionY\":1,\"status\":\"default\"},{\"id\":13,\"name\":\"扫地机器人\",\"imagePath\":\"/images/扫地机器人.png\",\"positionX\":4,\"positionY\":1,\"status\":\"default\"},{\"id\":14,\"name\":\"挂耳咖啡\",\"imagePath\":\"/images/挂耳咖啡.png\",\"positionX\":5,\"positionY\":1,\"status\":\"default\"},{\"id\":15,\"name\":\"医疗机器人\",\"imagePath\":\"/images/医疗机器人.png\",\"positionX\":6,\"positionY\":1,\"status\":\"default\"},{\"id\":16,\"name\":\"军用电台\",\"imagePath\":\"/images/军用电台.png\",\"positionX\":7,\"positionY\":1,\"status\":\"default\"},{\"id\":17,\"name\":\"无人机\",\"imagePath\":\"/images/无人机.png\",\"positionX\":0,\"positionY\":2,\"status\":\"default\"},{\"id\":18,\"name\":\"军用炮弹\",\"imagePath\":\"/images/军用炮弹.png\",\"positionX\":1,\"positionY\":2,\"status\":\"default\"},{\"id\":19,\"name\":\"金条\",\"imagePath\":\"/images/金条.png\",\"positionX\":2,\"positionY\":2,\"status\":\"default\"},{\"id\":20,\"name\":\"绝密服务器\",\"imagePath\":\"/images/绝密服务器.png\",\"positionX\":3,\"positionY\":2,\"status\":\"default\"},{\"id\":21,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":4,\"positionY\":2,\"status\":\"default\"},{\"id\":22,\"name\":\"鱼子酱\",\"imagePath\":\"/images/鱼子酱.png\",\"positionX\":5,\"positionY\":2,\"status\":\"default\"},{\"id\":23,\"name\":\"微型反应炉\",\"imagePath\":\"/images/微型反应炉.png\",\"positionX\":6,\"positionY\":2,\"status\":\"default\"},{\"id\":24,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":7,\"positionY\":2,\"status\":\"default\"},{\"id\":25,\"name\":\"高速磁盘阵列\",\"imagePath\":\"/images/高速磁盘阵列.png\",\"positionX\":0,\"positionY\":3,\"status\":\"default\"},{\"id\":26,\"name\":\"化石\",\"imagePath\":\"/images/化石.png\",\"positionX\":1,\"positionY\":3,\"status\":\"default\"},{\"id\":27,\"name\":\"主战坦克模型\",\"imagePath\":\"/images/坦克.png\",\"positionX\":2,\"positionY\":3,\"status\":\"default\"},{\"id\":28,\"name\":\"高级咖啡豆\",\"imagePath\":\"/images/咖啡豆.png\",\"positionX\":3,\"positionY\":3,\"status\":\"default\"},{\"id\":29,\"name\":\"强力吸尘器\",\"imagePath\":\"/images/强力吸尘器.png\",\"positionX\":4,\"positionY\":3,\"status\":\"default\"},{\"id\":30,\"name\":\"军用卫星通讯仪\",\"imagePath\":\"/images/军用卫星通讯仪.png\",\"positionX\":5,\"positionY\":3,\"status\":\"default\"},{\"id\":31,\"name\":\"雷斯的留声机\",\"imagePath\":\"/images/雷斯的留声机.png\",\"positionX\":6,\"positionY\":3,\"status\":\"default\"},{\"id\":32,\"name\":\"阵列服务器\",\"imagePath\":\"/images/阵列服务器.png\",\"positionX\":0,\"positionY\":4,\"status\":\"default\"},{\"id\":33,\"name\":\"实验数据\",\"imagePath\":\"/images/实验数据.png\",\"positionX\":1,\"positionY\":4,\"status\":\"default\"},{\"id\":34,\"name\":\"克劳狄乌斯半身像\",\"imagePath\":\"/images/半身像.png\",\"positionX\":2,\"positionY\":4,\"status\":\"default\"},{\"id\":35,\"name\":\"赛伊德的怀表\",\"imagePath\":\"/images/怀表.png\",\"positionX\":3,\"positionY\":4,\"status\":\"default\"},{\"id\":36,\"name\":\"自动体外除颤器\",\"imagePath\":\"/images/自动体外除颤仪.png\",\"positionX\":4,\"positionY\":4,\"status\":\"default\"},{\"id\":37,\"name\":\"奥莉薇娅香槟\",\"imagePath\":\"/images/香槟.png\",\"positionX\":5,\"positionY\":4,\"status\":\"default\"},{\"id\":38,\"name\":\"装甲车电池\",\"imagePath\":\"/images/装甲车电池.png\",\"positionX\":6,\"positionY\":4,\"status\":\"default\"},{\"id\":39,\"name\":\"红房卡\",\"imagePath\":\"/images/红卡.png\",\"positionX\":7,\"positionY\":4,\"status\":\"default\"},{\"id\":40,\"name\":\"飞行记录仪\",\"imagePath\":\"/images/黑匣子.png\",\"positionX\":0,\"positionY\":5,\"status\":\"default\"},{\"id\":41,\"name\":\"滑膛枪展品\",\"imagePath\":\"/images/滑膛枪.png\",\"positionX\":1,\"positionY\":5,\"status\":\"default\"},{\"id\":42,\"name\":\"步战车模型\",\"imagePath\":\"/images/步战车.png\",\"positionX\":2,\"positionY\":5,\"status\":\"default\"},{\"id\":43,\"name\":\"天圆地方\",\"imagePath\":\"/images/天圆地方.png\",\"positionX\":3,\"positionY\":5,\"status\":\"default\"},{\"id\":44,\"name\":\"复苏呼吸机\",\"imagePath\":\"/images/复苏呼吸机.png\",\"positionX\":4,\"positionY\":5,\"status\":\"default\"},{\"id\":45,\"name\":\"紫房卡\",\"imagePath\":\"/images/紫卡.png\",\"positionX\":5,\"positionY\":5,\"status\":\"default\"},{\"id\":46,\"name\":\"万金泪冠\",\"imagePath\":\"/images/万金泪冠.png\",\"positionX\":6,\"positionY\":5,\"status\":\"default\"},{\"id\":47,\"name\":\"量子存储\",\"imagePath\":\"/images/量子存储.png\",\"positionX\":0,\"positionY\":6,\"status\":\"default\"},{\"id\":48,\"name\":\"曼德尔超算单元\",\"imagePath\":\"/images/超算单元.png\",\"positionX\":1,\"positionY\":6,\"status\":\"default\"},{\"id\":49,\"name\":\"军用信息终端\",\"imagePath\":\"/images/大终端.png\",\"positionX\":2,\"positionY\":6,\"status\":\"default\"},{\"id\":50,\"name\":\"云存储阵列\",\"imagePath\":\"/images/云存储阵列.png\",\"positionX\":3,\"positionY\":6,\"status\":\"default\"},{\"id\":51,\"name\":\"G.T.I卫星通信天线\",\"imagePath\":\"/images/卫星锅.png\",\"positionX\":4,\"positionY\":6,\"status\":\"default\"},{\"id\":52,\"name\":\"动力电池组\",\"imagePath\":\"/images/动力电池组.png\",\"positionX\":5,\"positionY\":6,\"status\":\"default\"},{\"id\":53,\"name\":\"非洲之心\",\"imagePath\":\"/images/非洲之心.png\",\"positionX\":0,\"positionY\":7,\"status\":\"default\"},{\"id\":54,\"name\":\"炫彩麦小蛋\",\"imagePath\":\"/images/瞪羚.png\",\"positionX\":1,\"positionY\":7,\"status\":\"default\"}]', 7, b'0', '2025-07-30 15:37:13.423935', 1, '分享玩家', 0, '874adabd-5f9e-4e32-9619-b30a16468be0', '2025-07-30 15:37:13.423935');

INSERT INTO `game_sessions` VALUES (179, NULL, '{\"boardId\":1,\"width\":10,\"height\":9,\"items\":[{\"id\":531,\"itemId\":18,\"name\":\"坦克\",\"x\":0,\"y\":0,\"width\":3,\"height\":3,\"imagePath\":\"/images/tank.png\",\"isRemoved\":false},{\"id\":532,\"itemId\":18,\"name\":\"坦克\",\"x\":7,\"y\":6,\"width\":3,\"height\":3,\"imagePath\":\"/images/tank.png\",\"isRemoved\":false},{\"id\":533,\"itemId\":19,\"name\":\"劳力士\",\"x\":3,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":534,\"itemId\":19,\"name\":\"劳力士\",\"x\":5,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":535,\"itemId\":19,\"name\":\"劳力士\",\"x\":6,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":536,\"itemId\":19,\"name\":\"劳力士\",\"x\":9,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/watch.png\",\"isRemoved\":false},{\"id\":537,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":4,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":538,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":5,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":539,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":9,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":540,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":1,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":541,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":4,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":542,\"itemId\":20,\"name\":\"海洋之泪\",\"x\":2,\"y\":4,\"width\":1,\"height\":1,\"imagePath\":\"/images/pearl.png\",\"isRemoved\":false},{\"id\":543,\"itemId\":21,\"name\":\"非洲之心\",\"x\":5,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":544,\"itemId\":21,\"name\":\"非洲之心\",\"x\":8,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":545,\"itemId\":21,\"name\":\"非洲之心\",\"x\":8,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":546,\"itemId\":21,\"name\":\"非洲之心\",\"x\":6,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":547,\"itemId\":21,\"name\":\"非洲之心\",\"x\":4,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":548,\"itemId\":21,\"name\":\"非洲之心\",\"x\":6,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":549,\"itemId\":21,\"name\":\"非洲之心\",\"x\":3,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":550,\"itemId\":21,\"name\":\"非洲之心\",\"x\":1,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/diamond.png\",\"isRemoved\":false},{\"id\":551,\"itemId\":22,\"name\":\"怀表\",\"x\":6,\"y\":0,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":552,\"itemId\":22,\"name\":\"怀表\",\"x\":4,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":553,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":554,\"itemId\":22,\"name\":\"怀表\",\"x\":5,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":555,\"itemId\":22,\"name\":\"怀表\",\"x\":5,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":556,\"itemId\":22,\"name\":\"怀表\",\"x\":2,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/pocket_watch.png\",\"isRemoved\":false},{\"id\":557,\"itemId\":23,\"name\":\"军用终端\",\"x\":7,\"y\":0,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":558,\"itemId\":23,\"name\":\"军用终端\",\"x\":3,\"y\":1,\"width\":1,\"height\":2,\"imagePath\":\"/images/terminal.png\",\"isRemoved\":false},{\"id\":559,\"itemId\":24,\"name\":\"步战车\",\"x\":4,\"y\":3,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":560,\"itemId\":24,\"name\":\"步战车\",\"x\":1,\"y\":6,\"width\":3,\"height\":2,\"imagePath\":\"/images/ifv.png\",\"isRemoved\":false},{\"id\":565,\"itemId\":27,\"name\":\"量子存储\",\"x\":4,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":566,\"itemId\":27,\"name\":\"量子存储\",\"x\":7,\"y\":2,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":567,\"itemId\":27,\"name\":\"量子存储\",\"x\":4,\"y\":5,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":568,\"itemId\":27,\"name\":\"量子存储\",\"x\":6,\"y\":6,\"width\":1,\"height\":1,\"imagePath\":\"/images/usb.png\",\"isRemoved\":false},{\"id\":569,\"itemId\":28,\"name\":\"滑膛枪\",\"x\":9,\"y\":0,\"width\":1,\"height\":4,\"imagePath\":\"/images/musket.png\",\"isRemoved\":false},{\"id\":570,\"itemId\":28,\"name\":\"滑膛枪\",\"x\":0,\"y\":5,\"width\":1,\"height\":4,\"imagePath\":\"/images/musket.png\",\"isRemoved\":false},{\"id\":573,\"itemId\":29,\"name\":\"金条\",\"x\":3,\"y\":3,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":574,\"itemId\":29,\"name\":\"金条\",\"x\":8,\"y\":2,\"width\":1,\"height\":2,\"imagePath\":\"/images/gold_bar.png\",\"isRemoved\":false},{\"id\":579,\"itemId\":25,\"name\":\"化石\",\"x\":2,\"y\":5,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":580,\"itemId\":25,\"name\":\"化石\",\"x\":4,\"y\":7,\"width\":2,\"height\":1,\"imagePath\":\"/images/fossil.png\",\"isRemoved\":false},{\"id\":581,\"itemId\":26,\"name\":\"卫星锅\",\"x\":0,\"y\":3,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":582,\"itemId\":26,\"name\":\"卫星锅\",\"x\":7,\"y\":4,\"width\":2,\"height\":2,\"imagePath\":\"/images/satellite.png\",\"isRemoved\":false},{\"id\":583,\"itemId\":30,\"name\":\"实验数据\",\"x\":6,\"y\":1,\"width\":1,\"height\":1,\"imagePath\":\"/images/cd.png\",\"isRemoved\":false},{\"id\":584,\"itemId\":30,\"name\":\"实验数据\",\"x\":5,\"y\":8,\"width\":1,\"height\":1,\"imagePath\":\"/images/cd.png\",\"isRemoved\":false},{\"id\":585,\"itemId\":30,\"name\":\"实验数据\",\"x\":6,\"y\":7,\"width\":1,\"height\":1,\"imagePath\":\"/images/cd.png\",\"isRemoved\":false},{\"id\":586,\"itemId\":30,\"name\":\"实验数据\",\"x\":7,\"y\":3,\"width\":1,\"height\":1,\"imagePath\":\"/images/cd.png\",\"isRemoved\":false}],\"storedRed\":{\"red6\":0,\"red9\":0,\"red12\":0}}', 1, b'0', '2025-07-31 10:29:00.479696', 1, '分享玩家', 0, 'GAME_B6A91134BF6F4AA2', '2025-07-31 10:29:00.479696');

-- ----------------------------
-- Table structure for game_types
-- ----------------------------
DROP TABLE IF EXISTS `game_types`;
CREATE TABLE `game_types`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `game_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏代码',
  `game_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '游戏名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '游戏描述',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1.0.0' COMMENT '游戏版本',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `config_schema` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '游戏配置结构定义',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `game_code`(`game_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of game_types
-- ----------------------------
INSERT INTO `game_types` VALUES (1, 'lianliankan', '连连看', '经典的连连看消除游戏，考验观察力和策略思维', '1.0.0', 1, '{\"board_width\": 10, \"board_height\": 9, \"item_types\": [\"tank\", \"watch\", \"key\", \"glasses\", \"ball\", \"car\"]}', '2025-07-25 15:27:31', '2025-07-25 15:27:31');
INSERT INTO `game_types` VALUES (3, 'LUCKY_WHEEL', '小里幸运转盘', '基于CDK的转盘抽奖游戏', '1.0.0', 1, NULL, '2025-07-31 07:54:50', '2025-07-31 07:54:50');
INSERT INTO `game_types` VALUES (7, 'item_selection', '物品选择', '选择物品并确认的游戏，支持导入导出功能', '1.0.0', 1, '{\"grid_width\": 8, \"grid_height\": 8}', '2025-07-30 00:00:00', '2025-07-30 00:00:00');

-- ----------------------------
-- Table structure for item_selection_items
-- ----------------------------
DROP TABLE IF EXISTS `item_selection_items`;
CREATE TABLE `item_selection_items`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_active` bit(1) NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `position_x` int NOT NULL,
  `position_y` int NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `difficulty_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_position`(`position_x` ASC, `position_y` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 554 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of item_selection_items
-- ----------------------------
INSERT INTO `item_selection_items` VALUES (1, NULL, '/images/黄金瞪羚.png', b'1', '黄金瞪羚', 0, 0, NULL, 'normal');
INSERT INTO `item_selection_items` VALUES (553, '2025-07-30 13:01:47.000000', '/images/紫卡.png', b'1', '紫房卡', 8, 19, NULL, 'hard');
INSERT INTO `item_selection_items` VALUES (554, '2025-07-30 13:01:47.000000', '/images/医疗机器人.png', b'1', '医疗机械人', 9, 19, NULL, 'hard');

-- ----------------------------
-- Table structure for lucky_wheel_admin
-- ----------------------------
DROP TABLE IF EXISTS `lucky_wheel_admin`;
CREATE TABLE `lucky_wheel_admin`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `last_login_time` datetime(6) NULL DEFAULT NULL,
  `login_count` int NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_a1lr5y5k2xsjwn3rrfbb0f5gc`(`username` ASC) USING BTREE,
  UNIQUE INDEX `idx_admin_username`(`username` ASC) USING BTREE,
  INDEX `idx_admin_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lucky_wheel_admin
-- ----------------------------
INSERT INTO `lucky_wheel_admin` VALUES (1, '2025-07-31 07:51:30.625200', '系统管理员', '0:0:0:0:0:0:0:1', '2025-07-31 12:59:49.377225', 7, 'admin123', 'ACTIVE', '2025-07-31 12:59:49.378226', 'admin');

-- ----------------------------
-- Table structure for lucky_wheel_cdk
-- ----------------------------
DROP TABLE IF EXISTS `lucky_wheel_cdk`;
CREATE TABLE `lucky_wheel_cdk`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cdk_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `expire_time` datetime(6) NULL DEFAULT NULL,
  `first_used_at` datetime(6) NULL DEFAULT NULL,
  `last_used_at` datetime(6) NULL DEFAULT NULL,
  `max_spins` int NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `used_spins` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_1wdiw7qcuf31okfe0uy2aj4ge`(`cdk_code` ASC) USING BTREE,
  UNIQUE INDEX `idx_cdk_code`(`cdk_code` ASC) USING BTREE,
  INDEX `idx_cdk_status`(`status` ASC) USING BTREE,
  INDEX `idx_cdk_created`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lucky_wheel_cdk
-- ----------------------------
INSERT INTO `lucky_wheel_cdk` VALUES (6, 'LWQZIME8SM', '2025-07-31 11:58:01.422127', 'admin', '', NULL, '2025-07-31 11:58:12.732111', '2025-07-31 13:02:09.197458', 111111, 'ACTIVE', '2025-07-31 13:02:09.200921', 44);
INSERT INTO `lucky_wheel_cdk` VALUES (7, 'LW29U2I4IO', '2025-07-31 12:59:57.584192', 'admin', '', NULL, NULL, NULL, 54124124, 'ACTIVE', '2025-07-31 12:59:57.584192', 0);

-- ----------------------------
-- Table structure for lucky_wheel_prizes
-- ----------------------------
DROP TABLE IF EXISTS `lucky_wheel_prizes`;
CREATE TABLE `lucky_wheel_prizes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `image_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_active` bit(1) NULL DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `probability` decimal(5, 4) NOT NULL,
  `remaining_quantity` int NOT NULL,
  `sort_order` int NULL DEFAULT NULL,
  `total_quantity` int NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_prize_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_prize_probability`(`probability` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lucky_wheel_prizes
-- ----------------------------
INSERT INTO `lucky_wheel_prizes` VALUES (1, NULL, '恭喜获得一等奖！价值1000元奖品', '/images/prize1.png', b'1', '一等奖', 0.0200, 3, 1, 5, '2025-07-31 12:43:43.218609');
INSERT INTO `lucky_wheel_prizes` VALUES (2, NULL, '恭喜获得二等奖！价值500元奖品', '/images/prize2.png', b'1', '二等奖', 0.0500, 5, 5, 10, '2025-07-31 12:57:41.925596');
INSERT INTO `lucky_wheel_prizes` VALUES (3, NULL, '恭喜获得三等奖！价值200元奖品', '/images/prize3.png', b'1', '三等奖', 0.1000, 8, 4, 20, '2025-07-31 12:58:27.099462');
INSERT INTO `lucky_wheel_prizes` VALUES (4, NULL, '恭喜获得四等奖！价值100元奖品', '/images/prize4.png', b'1', '四等奖', 0.1500, 31, 3, 50, '2025-07-31 13:02:04.751730');
INSERT INTO `lucky_wheel_prizes` VALUES (5, NULL, '恭喜获得五等奖！价值50元奖品', '/images/prize5.png', b'1', '五等奖', 0.2000, 79, 0, 100, '2025-07-31 13:02:09.200921');
INSERT INTO `lucky_wheel_prizes` VALUES (6, NULL, '感谢参与！获得纪念品一份', '/images/prize6.png', b'1', '参与奖', 0.3000, 177, 2, 200, '2025-07-31 12:57:26.908958');

-- ----------------------------
-- Table structure for lucky_wheel_spin_results
-- ----------------------------
DROP TABLE IF EXISTS `lucky_wheel_spin_results`;
CREATE TABLE `lucky_wheel_spin_results`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cdk_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_winning_spin` bit(1) NULL DEFAULT NULL,
  `prize_id` bigint NULL DEFAULT NULL,
  `prize_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `spin_time` datetime(6) NULL DEFAULT NULL,
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_spin_cdk`(`cdk_code` ASC) USING BTREE,
  INDEX `idx_spin_prize`(`prize_id` ASC) USING BTREE,
  INDEX `idx_spin_time`(`spin_time` ASC) USING BTREE,
  INDEX `idx_spin_session`(`session_id` ASC) USING BTREE,
  CONSTRAINT `FK6vptrjkfuhgu2xxt1ht1kk2sw` FOREIGN KEY (`cdk_code`) REFERENCES `lucky_wheel_cdk` (`cdk_code`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKfh5e4bxpoo6xiodl7jl9l2s3y` FOREIGN KEY (`prize_id`) REFERENCES `lucky_wheel_prizes` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lucky_wheel_spin_results
-- ----------------------------
INSERT INTO `lucky_wheel_spin_results` VALUES (38, 'LWQZIME8SM', '0:0:0:0:0:0:0:1', b'1', 6, '参与奖', 'LW_1753934291196_8D28EX', '2025-07-31 11:58:12.734288', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********');
INSERT INTO `lucky_wheel_spin_results` VALUES (39, 'LWQZIME8SM', '0:0:0:0:0:0:0:1', b'1', 6, '参与奖', 'LW_1753934291196_8D28EX', '2025-07-31 11:59:47.587677', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********');
INSERT INTO `lucky_wheel_spin_results` VALUES (83, 'LWQZIME8SM', '0:0:0:0:0:0:0:1', b'1', 5, '五等奖', 'LW_1753938116363_F9C1NE', '2025-07-31 13:02:09.199882', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********');

-- ----------------------------
-- View structure for board_layout_view
-- ----------------------------
DROP VIEW IF EXISTS `board_layout_view`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `board_layout_view` AS select `bip`.`x` AS `x`,`bip`.`y` AS `y`,`gi`.`name` AS `item_name`,substr(`gi`.`name`,1,2) AS `short_name`,`bip`.`is_removed` AS `is_removed` from (`board_item_position` `bip` join `game_item` `gi` on((`bip`.`item_id` = `gi`.`id`))) where (`bip`.`board_id` = 1);

SET FOREIGN_KEY_CHECKS = 1;
